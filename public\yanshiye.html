<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户登录系统</title>
  <link rel="stylesheet" href="styles.css">
  <style>
    /* Specific styles for index.html, if any, can go here or be merged into styles.css */
    /* Example: activation-status specific styling moved to styles.css */

    /* 转移按钮样式 */
    .transfer-btn {
      background-color: #17a2b8;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      margin-left: 4px;
    }

    .transfer-btn:hover {
      background-color: #138496;
    }

    /* 批量选择相关样式 */
    .selected-accounts-list {
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    .selected-account-item {
      padding: 5px;
      margin: 2px 0;
      background-color: #e9ecef;
      border-radius: 3px;
      font-size: 14px;
    }

    .account-checkbox {
      margin-right: 8px;
    }
  </style>
</head>
<body class="login-body">
  <div class="yanshiye-container">
    <h1>用户登录系统</h1>

    <div id="auth-container" class="card">
      <div class="tabs">
        <div class="tab active" id="login-tab">登录</div>
        <div class="tab" id="register-tab">注册</div>
      </div>

      <!-- 登录表单 -->
      <form id="login-form" class="form-content">
        <div class="form-group">
          <label for="login-phone">手机号</label>
          <input type="text" id="login-phone" required placeholder="请输入手机号">
        </div>
        <div class="form-group">
          <label for="login-password">密码</label>
          <input type="password" id="login-password" required placeholder="请输入密码">
        </div>
        <div class="form-group">
          <label for="client-version">客户端版本号（演示用）</label>
          <input type="text" id="client-version" placeholder="例如: 1.0.0" value="1.0.0">
        </div>
        <button type="submit" class="button button-primary button-full-width">登录</button>
      </form>

      <!-- 注册表单 -->
      <form id="register-form" class="form-content hidden">
        <div class="form-group">
          <label for="register-phone">手机号</label>
          <input type="text" id="register-phone" required placeholder="请输入手机号">
        </div>
        <div class="form-group">
          <label for="register-password">密码</label>
          <input type="password" id="register-password" required placeholder="请输入密码">
        </div>
        <div class="form-group">
          <label for="register-confirm-password">确认密码</label>
          <input type="password" id="register-confirm-password" required placeholder="请再次输入密码">
        </div>
        <button type="submit" class="button button-primary button-full-width">注册</button>
      </form>

      <div id="message" class="message hidden"></div>
    </div>

    <!-- 登录成功后显示的内容 -->
    <div id="user-container" class="main-dashboard hidden">
      <!-- 顶部用户信息栏 -->
      <div class="top-user-bar">
        <div class="user-info-summary">
          <h2>欢迎回来!</h2>
          <div class="user-basic-info">
            <span class="user-phone-display">手机号: <span id="user-phone"></span></span>
            <span id="account-type" class="account-type-badge">主账号</span>
            <span class="status-badge status-online">已登录</span>
            <span id="activation-status" class="status-badge">未激活</span>
          </div>
          <div id="account-owner-container" class="hidden">
            <span>归属主账号: <span id="account-owner">无</span></span>
          </div>
          <div id="expiry-date-container" class="hidden">
            <span>到期时间: <span id="expiry-date">无</span></span>
          </div>
        </div>
        <div class="top-actions">
          <button id="logout-btn" class="button button-danger">退出登录</button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧功能面板 -->
        <div class="left-sidebar">
          <!-- 激活码输入区域 -->
          <div id="activation-area" class="function-card">
            <h3>激活账户</h3>
            <div class="form-group">
              <input type="text" id="activation-code" placeholder="请输入激活码" class="form-control">
              <button id="activate-btn" class="button button-primary button-full-width">激活</button>
            </div>
          </div>

          <!-- 子账号管理区域（仅主账号显示） -->
          <div id="sub-account-management" class="function-card hidden">
            <h3>子账号管理</h3>
            <div class="function-actions">
              <button id="add-sub-account-btn" class="button button-primary button-full-width">添加子账号</button>
              <button id="refresh-sub-accounts-btn" class="button button-secondary button-full-width">刷新列表</button>
            </div>
            <div id="sub-accounts-list" class="sub-accounts-container">
              <p class="no-sub-accounts">暂无子账号</p>
            </div>
          </div>
        </div>

        <!-- 右侧平台账号管理区域 -->
        <div class="right-main">
          <div class="platform-section">
            <div class="section-header">
              <h3>我的平台账号</h3>
              <div class="search-controls">
                <input type="text" id="platform-search-input" placeholder="输入平台手机号搜索..." class="search-input">
                <button id="search-platform-btn" class="button button-primary">搜索</button>
                <button id="add-platform-account-btn" class="button button-success">添加平台账号</button>
                <button id="batch-transfer-btn" class="button button-warning" style="display: none;">批量转移</button>
                <button id="select-all-btn" class="button button-secondary">全选</button>
                <button id="clear-selection-btn" class="button button-secondary">清空选择</button>
              </div>
            </div>

            <div id="platform-accounts-display" class="platform-table-container">
              <table class="platform-table">
                <thead>
                  <tr>
                    <th><input type="checkbox" id="select-all-checkbox"> 选择</th>
                    <th>所属账号</th>
                    <th>平台手机号</th>
                    <th>平台</th>
                    <th>用户名</th>
                    <th>内容类型</th>
                    <th>团队标签</th>
                    <th>认证状态</th>
                    <th>账户状态</th>
                    <th>粉丝数</th>
                    <th>信用分</th>
                    <th>总收益</th>
                    <th>昨日收益</th>
                    <th>可提现收益</th>
                    <th>总阅读</th>
                    <th>昨日阅读</th>
                    <th>草稿箱</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="platform-accounts-tbody">
                  <tr>
                    <td colspan="18" class="no-data">暂无平台账号信息。</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 强制登录确认模态框 -->
  <div id="force-login-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">账号已在其他设备登录</h3>
      </div>
      <div class="modal-body">
        <p>您的账号目前已在其他设备登录，是否强制登录？强制登录将会使其他设备下线。</p>
      </div>
      <div class="modal-footer">
        <button id="cancel-force-login" class="button button-secondary" type="button">取消</button>
        <button id="confirm-force-login" class="button button-primary" type="button">强制登录</button>
      </div>
    </div>
  </div>

  <!-- 版本升级模态框 -->
  <div id="upgrade-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">客户端版本需要升级</h3>
      </div>
      <div class="modal-body">
        <p>您的客户端版本过低，请升级到最新版本。</p>
        <p>当前版本: <span id="current-version"></span></p>
        <p>最新版本: <span id="latest-version"></span></p>
      </div>
      <div class="modal-footer">
        <button id="confirm-upgrade" class="button button-primary" type="button">确认</button>
      </div>
    </div>
  </div>

  <!-- 创建子账号模态框 -->
  <div id="create-sub-account-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">创建子账号</h3>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="sub-phone">手机号</label>
          <input type="text" id="sub-phone" class="form-control" placeholder="请输入手机号">
        </div>
        <div class="form-group">
          <label for="sub-password">密码</label>
          <input type="password" id="sub-password" class="form-control" placeholder="请输入密码">
        </div>
        <div class="form-group">
          <label for="sub-confirm-password">确认密码</label>
          <input type="password" id="sub-confirm-password" class="form-control" placeholder="请再次输入密码">
        </div>
        <div id="sub-account-create-message" class="message hidden"></div>
      </div>
      <div class="modal-footer">
        <button id="cancel-create-sub-account" class="button button-secondary" type="button">取消</button>
        <button id="confirm-create-sub-account" class="button button-primary" type="button">创建</button>
      </div>
    </div>
  </div>

  <!-- 添加平台账号模态框 -->
  <div id="add-platform-account-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">添加平台账号</h3>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="platform-phone">平台手机号</label>
          <input type="text" id="platform-phone" class="form-control" placeholder="请输入平台手机号" required>
        </div>
        <div class="form-group">
          <label for="platform-type">平台</label>
          <select id="platform-type" class="form-control" required>
            <option value="">请选择平台</option>
            <option value="头条号">头条号</option>
            <option value="百家号">百家号</option>
          </select>
        </div>
        <div class="form-group">
          <label for="content-type">内容类型</label>
          <select id="content-type" class="form-control" required>
            <option value="">请选择内容类型</option>
            <option value="视频">视频</option>
            <option value="文章">文章</option>
            <option value="微头条">微头条</option>
          </select>
        </div>
        <div class="form-group">
          <label for="platform-sessionid">SessionID</label>
          <input type="text" id="platform-sessionid" class="form-control" placeholder="请输入SessionID" required>
        </div>
        <div id="platform-account-create-message" class="message hidden"></div>
      </div>
      <div class="modal-footer">
        <button id="cancel-add-platform-account" class="button button-secondary" type="button">取消</button>
        <button id="confirm-add-platform-account" class="button button-primary" type="button">添加</button>
      </div>
    </div>
  </div>

  <!-- 转移平台账号模态框 -->
  <div id="transfer-platform-account-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">转移平台账号</h3>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="transfer-new-holder-select">选择新归属用户:</label>
          <select id="transfer-new-holder-select" class="form-control">
            <option value="">请选择用户...</option>
          </select>
        </div>
        <div id="transfer-account-message" class="message hidden"></div>
      </div>
      <div class="modal-footer">
        <button id="cancel-transfer-account" class="button button-secondary">取消</button>
        <button id="confirm-transfer-account" class="button button-primary">确认转移</button>
      </div>
    </div>
  </div>

  <!-- 批量转移平台账号模态框 -->
  <div id="batch-transfer-modal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">批量转移平台账号</h3>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>已选择的平台账号:</label>
          <div id="selected-accounts-list" class="selected-accounts-list"></div>
        </div>
        <div class="form-group">
          <label for="batch-transfer-new-holder-select">选择新归属用户:</label>
          <select id="batch-transfer-new-holder-select" class="form-control">
            <option value="">请选择用户...</option>
          </select>
        </div>
        <div id="batch-transfer-message" class="message hidden"></div>
      </div>
      <div class="modal-footer">
        <button id="cancel-batch-transfer" class="button button-secondary">取消</button>
        <button id="confirm-batch-transfer" class="button button-primary">确认批量转移</button>
      </div>
    </div>
  </div>

  <script>
    // API基本URL
    const API_URL = window.location.origin;
    let sessionId = null;
    let sseConnection = null;
    let tempLoginData = null; // 存储临时登录数据

    // DOM元素
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const messageDiv = document.getElementById('message');
    const authContainer = document.getElementById('auth-container');
    const userContainer = document.getElementById('user-container');
    const userPhoneSpan = document.getElementById('user-phone');
    const logoutBtn = document.getElementById('logout-btn');
    const forceLoginModal = document.getElementById('force-login-modal');
    const cancelForceLoginBtn = document.getElementById('cancel-force-login');
    const confirmForceLoginBtn = document.getElementById('confirm-force-login');
    const activateBtn = document.getElementById('activate-btn');
    const activationStatusSpan = document.getElementById('activation-status');
    const activationCodeInput = document.getElementById('activation-code');
    const upgradeModal = document.getElementById('upgrade-modal');
    const confirmUpgradeBtn = document.getElementById('confirm-upgrade');
    const clientVersionInput = document.getElementById('client-version');
    const expiryDateContainer = document.getElementById('expiry-date-container');
    const expiryDateSpan = document.getElementById('expiry-date');

    // 子账号管理相关DOM元素
    const accountTypeSpan = document.getElementById('account-type');
    const accountOwnerContainer = document.getElementById('account-owner-container');
    const accountOwnerSpan = document.getElementById('account-owner');
    const subAccountManagement = document.getElementById('sub-account-management');
    const addSubAccountBtn = document.getElementById('add-sub-account-btn');
    const refreshSubAccountsBtn = document.getElementById('refresh-sub-accounts-btn');
    const subAccountsList = document.getElementById('sub-accounts-list');
    const createSubAccountModal = document.getElementById('create-sub-account-modal');
    const subPhoneInput = document.getElementById('sub-phone');
    const subPasswordInput = document.getElementById('sub-password');
    const subConfirmPasswordInput = document.getElementById('sub-confirm-password');
    const cancelCreateSubAccountBtn = document.getElementById('cancel-create-sub-account');
    const confirmCreateSubAccountBtn = document.getElementById('confirm-create-sub-account');
    const subAccountCreateMessage = document.getElementById('sub-account-create-message');

    // 平台账号管理相关DOM元素
    const viewPlatformAccountsBtn = document.getElementById('view-platform-accounts-btn');
    const refreshPlatformAccountsBtn = document.getElementById('refresh-platform-accounts-btn');
    const platformAccountsDisplay = document.getElementById('platform-accounts-display');
    const platformAccountsList = document.getElementById('platform-accounts-list');

    // 添加平台账号模态框相关DOM元素
    const addPlatformAccountModal = document.getElementById('add-platform-account-modal');
    const platformPhoneInput = document.getElementById('platform-phone');
    const platformTypeSelect = document.getElementById('platform-type');
    const contentTypeSelect = document.getElementById('content-type');
    const platformSessionidInput = document.getElementById('platform-sessionid');
    const cancelAddPlatformAccountBtn = document.getElementById('cancel-add-platform-account');
    const confirmAddPlatformAccountBtn = document.getElementById('confirm-add-platform-account');
    const platformAccountCreateMessage = document.getElementById('platform-account-create-message');

    // 转移平台账号模态框相关DOM元素
    const transferPlatformAccountModal = document.getElementById('transfer-platform-account-modal');
    const transferNewHolderSelect = document.getElementById('transfer-new-holder-select');
    const cancelTransferAccountBtn = document.getElementById('cancel-transfer-account');
    const confirmTransferAccountBtn = document.getElementById('confirm-transfer-account');
    const transferAccountMessage = document.getElementById('transfer-account-message');


    // 当前用户信息
    let currentUser = null;
    let allPlatformAccounts = []; // 存储所有平台账号数据
    let userPhoneMap = new Map(); // 存储用户ID到手机号的映射

    // 转移相关变量
    let currentTransferAccountPhone = null;

    // 批量选择相关变量
    let selectedAccounts = new Set(); // 存储选中的平台账号

    // 批量转移相关DOM元素
    const batchTransferModal = document.getElementById('batch-transfer-modal');
    const batchTransferNewHolderSelect = document.getElementById('batch-transfer-new-holder-select');
    const batchTransferMessage = document.getElementById('batch-transfer-message');
    const selectedAccountsList = document.getElementById('selected-accounts-list');
    const batchTransferBtn = document.getElementById('batch-transfer-btn');
    const selectAllBtn = document.getElementById('select-all-btn');
    const clearSelectionBtn = document.getElementById('clear-selection-btn');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const cancelBatchTransferBtn = document.getElementById('cancel-batch-transfer');
    const confirmBatchTransferBtn = document.getElementById('confirm-batch-transfer');

    // 切换标签页
    loginTab.addEventListener('click', () => {
      loginTab.classList.add('active');
      registerTab.classList.remove('active');
      loginForm.classList.remove('hidden');
      registerForm.classList.add('hidden');
      messageDiv.classList.add('hidden');
    });

    registerTab.addEventListener('click', () => {
      registerTab.classList.add('active');
      loginTab.classList.remove('active');
      registerForm.classList.remove('hidden');
      loginForm.classList.add('hidden');
      messageDiv.classList.add('hidden');
    });

    // 显示消息
    function showMessage(text, type, targetDiv = messageDiv) {
      targetDiv.textContent = text;
      targetDiv.className = `message message-${type}`; // Updated class name
      targetDiv.classList.remove('hidden');

      setTimeout(() => {
        targetDiv.classList.add('hidden');
      }, 5000);
    }

    // 检查并尝试自动登录
    async function checkSession() {
      const savedSessionId = localStorage.getItem('sessionId');
      if (savedSessionId) {
        sessionId = savedSessionId;

        try {
          const response = await fetch(`${API_URL}/auth/check-session`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sessionId })
          });
          const data = await response.json();

          if (data.success) {
            // 会话有效，如果有userId则直接设置基本用户信息，然后获取详细信息
            if (data.userId) {
              currentUser = {
                id: data.userId,
                phone: data.phone
              };
            }
            await loadUserInfo(data.phone);
            connectSSE();
          } else {
            localStorage.removeItem('sessionId');
          }
        } catch (err) {
          console.error('检查会话失败:', err);
          localStorage.removeItem('sessionId');
        }
      }
    }

    // 显示登录用户信息
    function showLoggedInUser(phone, userInfo = null) {
      userPhoneSpan.textContent = phone;

      // 如果有用户信息，显示账号类型和归属
      if (userInfo) {
        currentUser = userInfo;

        // 显示账号类型
        accountTypeSpan.textContent = userInfo.account_type || '主账号';
        accountTypeSpan.className = `account-type-badge ${userInfo.account_type === '主账号' ? 'type-main' : 'type-sub'}`;

        // 显示归属信息（仅子账号）
        if (userInfo.account_type === '子账号' && userInfo.owner_phone) {
          accountOwnerContainer.classList.remove('hidden');
          accountOwnerSpan.textContent = userInfo.owner_phone;
        } else {
          accountOwnerContainer.classList.add('hidden');
        }

        // 显示子账号管理区域（仅主账号）
        if (userInfo.account_type === '主账号') {
          subAccountManagement.classList.remove('hidden');
          loadSubAccounts();
        } else {
          subAccountManagement.classList.add('hidden');
        }

        // 初始化用户手机号映射
        initializeUserPhoneMap();

        // 显示激活功能（仅主账号）
        const activationArea = document.getElementById('activation-area');
        if (userInfo.account_type === '主账号') {
          activationArea.classList.remove('hidden');
        } else {
          activationArea.classList.add('hidden');
        }

        // 处理激活状态显示
        const expiryDate = userInfo.expiry_date ? new Date(userInfo.expiry_date) : null;
        const now = new Date();

        if (expiryDate && expiryDate > now) {
          // 已激活状态
          activationStatusSpan.textContent = '已激活';
          activationStatusSpan.className = 'status-badge status-online';
          expiryDateContainer.classList.remove('hidden');
          expiryDateSpan.textContent = formatDate(expiryDate);
        } else if (expiryDate && expiryDate <= now) {
          // 已过期状态
          activationStatusSpan.textContent = '已过期';
          activationStatusSpan.className = 'status-badge status-expired';
          expiryDateContainer.classList.remove('hidden');
          expiryDateSpan.textContent = formatDate(expiryDate) + ' (已过期)';
        } else {
          // 未激活状态
          activationStatusSpan.textContent = '未激活';
          activationStatusSpan.className = 'status-badge status-offline';
          expiryDateContainer.classList.add('hidden');
        }
      }

      authContainer.classList.add('hidden');
      userContainer.classList.remove('hidden');

      // 默认显示平台账号信息
      platformAccountsDisplay.classList.remove('hidden');
      loadPlatformAccounts();
    }

    // 加载用户详细信息
    async function loadUserInfo(phone) {
      try {
        const response = await fetch(`${API_URL}/api/user/user-info?phone=${encodeURIComponent(phone)}&sessionId=${sessionId}`);
        const data = await response.json();

        if (data.success && data.user) {
          showLoggedInUser(phone, data.user);
        } else {
          // 如果获取用户信息失败，保持现有的currentUser（如果有ID）或创建基本信息
          if (!currentUser || !currentUser.id) {
            currentUser = { phone: phone };
          }
          showLoggedInUser(phone);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取用户信息失败，保持现有的currentUser（如果有ID）或创建基本信息
        if (!currentUser || !currentUser.id) {
          currentUser = { phone: phone };
        }
        showLoggedInUser(phone);
      }
    }

    // 加载子账号列表
    async function loadSubAccounts() {
      if (!currentUser || currentUser.account_type !== '主账号') {
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/user/sub-accounts?ownerId=${currentUser.id}&sessionId=${sessionId}`);
        const data = await response.json();

        if (data.success) {
          // 更新currentUser的子账号信息
          if (currentUser) {
            currentUser.sub_accounts = data.subAccounts || [];
          }
          displaySubAccounts(data.subAccounts || []);
          // 重新初始化用户映射
          initializeUserPhoneMap();
        } else {
          console.error('获取子账号列表失败:', data.message);
          subAccountsList.innerHTML = '<p class="no-sub-accounts">获取子账号列表失败</p>';
        }
      } catch (error) {
        console.error('获取子账号列表失败:', error);
        subAccountsList.innerHTML = '<p class="no-sub-accounts">网络错误，无法获取子账号列表</p>';
      }
    }

    // 显示子账号列表
    function displaySubAccounts(subAccounts) {
      if (subAccounts.length === 0) {
        subAccountsList.innerHTML = '<p class="no-sub-accounts">暂无子账号</p>';
        return;
      }

      const subAccountsHtml = subAccounts.map(account => {
        const createdTime = new Date(account.created_at).toLocaleString();
        const lastLoginTime = account.last_login_at ? new Date(account.last_login_at).toLocaleString() : '从未登录';

        return `
          <div class="sub-account-item">
            <div class="sub-account-info">
              <div class="phone">${account.phone}</div>
              <div class="created-time">创建时间: ${createdTime}</div>
              <div class="created-time">最后登录: ${lastLoginTime}</div>
            </div>
            <div class="sub-account-actions">
              <button class="button button-danger" onclick="deleteSubAccount(${account.id}, '${account.phone}')">删除</button>
            </div>
          </div>
        `;
      }).join('');

      subAccountsList.innerHTML = subAccountsHtml;
    }

    // 初始化用户手机号映射
    function initializeUserPhoneMap() {
      // 清空现有映射
      userPhoneMap.clear();

      // 添加当前用户
      if (currentUser && currentUser.id && currentUser.phone) {
        userPhoneMap.set(currentUser.id, currentUser.phone);
      }

      // 如果是主账号，添加子账号映射
      if (currentUser && currentUser.sub_accounts) {
        currentUser.sub_accounts.forEach(subAccount => {
          if (subAccount.id && subAccount.phone) {
            userPhoneMap.set(subAccount.id, subAccount.phone);
          }
        });
      }
    }

    // 根据用户ID获取手机号
    function getHolderPhoneById(userId) {
      return userPhoneMap.get(userId) || '-';
    }

    // 显示创建子账号模态框
    function showCreateSubAccountModal() {
      // 清空表单
      subPhoneInput.value = '';
      subPasswordInput.value = '';
      subConfirmPasswordInput.value = '';
      subAccountCreateMessage.classList.add('hidden');

      createSubAccountModal.classList.remove('hidden');
    }

    // 隐藏创建子账号模态框
    function hideCreateSubAccountModal() {
      createSubAccountModal.classList.add('hidden');
    }

    // 创建子账号
    async function createSubAccount() {
      const phone = subPhoneInput.value.trim();
      const password = subPasswordInput.value;
      const confirmPassword = subConfirmPasswordInput.value;

      // 验证输入
      if (!phone || !password || !confirmPassword) {
        showMessage('请填写所有字段', 'error', subAccountCreateMessage);
        return;
      }

      if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致', 'error', subAccountCreateMessage);
        return;
      }

      if (password.length < 6) {
        showMessage('密码长度至少6位', 'error', subAccountCreateMessage);
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/user/create-sub-account`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ownerId: currentUser.id,
            phone: phone,
            password: password,
            sessionId: sessionId
          })
        });

        const data = await response.json();

        if (data.success) {
          showMessage('子账号创建成功', 'success', subAccountCreateMessage);
          setTimeout(() => {
            hideCreateSubAccountModal();
            loadSubAccounts(); // 刷新子账号列表
            showMessage('子账号创建成功', 'success');
          }, 1500);
        } else {
          showMessage(data.message || '创建子账号失败', 'error', subAccountCreateMessage);
        }
      } catch (error) {
        console.error('创建子账号失败:', error);
        showMessage('网络错误，请稍后重试', 'error', subAccountCreateMessage);
      }
    }

    // 平台账号管理函数
    async function loadPlatformAccounts() {
      if (!currentUser || !currentUser.id) {
        return;
      }

      try {
        // 判断是否为主账号
        const isMainAccount = currentUser.account_type === '主账号';
        const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${currentUser.id}&is_main_account=${isMainAccount}&sessionId=${sessionId}`);
        const data = await response.json();

        if (data.success) {
          allPlatformAccounts = data.accountInfos || []; // 存储所有账号数据
          displayPlatformAccounts(data.accountInfos || []);
        } else {
          // 统一错误处理：显示具体错误信息
          console.error('获取平台账号失败:', data.message);
          showMessage(data.message || '获取平台账号失败', 'error');
          allPlatformAccounts = [];
          displayPlatformAccounts([]);
        }
      } catch (error) {
        // 统一错误处理：区分网络错误和其他错误
        console.error('获取平台账号失败:', error);
        showMessage('网络错误，无法获取平台账号信息', 'error');
        displayPlatformAccounts([]);
      }
    }

    function displayPlatformAccounts(accounts) {
      const tbody = document.getElementById('platform-accounts-tbody');

      if (accounts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="18" class="no-platform-accounts">暂无平台账号信息。</td></tr>';
        return;
      }

      const rowsHtml = accounts.map(account => {
        // 修复：stats已经是对象，不需要再次解析
        const stats = account.stats && typeof account.stats === 'object' ? account.stats :
                     (typeof account.stats === 'string' ? JSON.parse(account.stats) : {});
        const loginTime = account.login_time ? new Date(account.login_time).toLocaleString() : '-';

        // 根据current_holder_id确定所属账号显示
        const holderPhone = getHolderPhoneById(account.current_holder_id);

        return `
          <tr>
            <td><input type="checkbox" class="account-checkbox" data-phone="${account.phone}" onchange="handleAccountSelection(this)"></td>
            <td class="owner-cell">${holderPhone}</td>
            <td class="phone-cell">${account.phone}</td>
            <td>${account.platform || '头条号'}</td>
            <td>${account.username || '-'}</td>
            <td>${account.login_type || '-'}</td>
            <td>${account.team_tag || '-'}</td>
            <td>
              <span class="verification-badge ${account.is_verified === '是' ? 'verified' : 'unverified'}">
                ${account.is_verified || '否'}
              </span>
            </td>
            <td>
              <span class="account-status ${
                account.account_status === '正常' ? 'status-normal' :
                account.account_status === '掉线' ? 'status-offline' :
                'status-abnormal'
              }">
                ${account.account_status || '未知'}
              </span>
            </td>
            <td class="number-cell">${stats.followers || '-'}</td>
            <td class="number-cell">${stats.credit_score || '-'}</td>
            <td class="number-cell">${stats.total_income || '-'}</td>
            <td class="number-cell">${stats.yesterday_income || '-'}</td>
            <td class="withdraw-amount-cell">${stats.can_withdraw_amount || '-'}</td>
            <td class="number-cell">${stats.total_reads || '-'}</td>
            <td class="number-cell">${stats.yesterday_reads || '-'}</td>
            <td class="number-cell">${account.drafts_count || '-'}</td>
            <td class="action-cell">
              <button class="action-btn edit-btn" onclick="editPlatformAccount('${account.phone}')">编辑</button>
              <button class="action-btn delete-btn" onclick="deletePlatformAccount('${account.phone}')">删除</button>
              ${currentUser && currentUser.account_type === '主账号' ?
                `<button class="action-btn transfer-btn" onclick="transferPlatformAccount('${account.phone}')">转移</button>` :
                ''}
            </td>
          </tr>
        `;
      }).join('');

      tbody.innerHTML = rowsHtml;

      // 重置选择状态
      selectedAccounts.clear();
      updateBatchTransferButton();
      updateSelectAllCheckbox();
    }

    // 批量选择相关函数
    function handleAccountSelection(checkbox) {
        const phone = checkbox.dataset.phone;

        if (checkbox.checked) {
            selectedAccounts.add(phone);
        } else {
            selectedAccounts.delete(phone);
        }

        updateBatchTransferButton();
        updateSelectAllCheckbox();
    }

    function updateBatchTransferButton() {
        if (selectedAccounts.size > 0 && currentUser && currentUser.account_type === '主账号') {
            batchTransferBtn.style.display = 'inline-block';
            batchTransferBtn.textContent = `批量转移 (${selectedAccounts.size})`;
        } else {
            batchTransferBtn.style.display = 'none';
        }
    }

    function updateSelectAllCheckbox() {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        const checkedCount = document.querySelectorAll('.account-checkbox:checked').length;

        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === checkboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    function selectAllAccounts() {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedAccounts.add(checkbox.dataset.phone);
        });
        updateBatchTransferButton();
        updateSelectAllCheckbox();
    }

    function clearAllSelection() {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectedAccounts.clear();
        updateBatchTransferButton();
        updateSelectAllCheckbox();
    }

    // 批量转移相关函数
    async function showBatchTransferModal() {
        if (selectedAccounts.size === 0) {
            showMessage('请先选择要转移的平台账号', 'error');
            return;
        }

        if (!currentUser || currentUser.account_type !== '主账号') {
            showMessage('只有主账号可以转移平台账号', 'error');
            return;
        }

        // 显示选中的账号列表
        selectedAccountsList.innerHTML = Array.from(selectedAccounts).map(phone =>
            `<div class="selected-account-item">${phone}</div>`
        ).join('');

        // 清空并加载转移选项
        batchTransferNewHolderSelect.innerHTML = '<option value="">正在加载用户列表...</option>';
        batchTransferMessage.classList.add('hidden');

        // 显示模态框
        batchTransferModal.classList.remove('hidden');

        // 加载转移选项
        await loadBatchTransferUserOptions();
    }

    async function loadBatchTransferUserOptions() {
        try {
            // 重置选项
            batchTransferNewHolderSelect.innerHTML = '<option value="">请选择用户...</option>';

            // 添加主账号选项（当前用户）
            const mainOption = document.createElement('option');
            mainOption.value = currentUser.id;
            mainOption.textContent = `${currentUser.phone} (主账号)`;
            batchTransferNewHolderSelect.appendChild(mainOption);

            // 获取子账号列表
            const response = await fetch(`${API_URL}/api/user/sub-accounts?ownerId=${currentUser.id}&sessionId=${sessionId}`);
            const data = await response.json();

            if (data.success && data.subAccounts) {
                data.subAccounts.forEach(subAccount => {
                    const option = document.createElement('option');
                    option.value = subAccount.id;
                    option.textContent = `${subAccount.phone} (子账号)`;
                    batchTransferNewHolderSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载批量转移用户选项失败:', error);
            batchTransferNewHolderSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            showMessage('加载用户列表失败', 'error', batchTransferMessage);
        }
    }

    function hideBatchTransferModal() {
        batchTransferModal.classList.add('hidden');
    }

    // 批量转移确认函数
    async function confirmBatchTransfer() {
        const newHolderId = parseInt(batchTransferNewHolderSelect.value);

        if (!newHolderId) {
            showMessage('请选择有效的用户', 'error', batchTransferMessage);
            return;
        }

        if (selectedAccounts.size === 0) {
            showMessage('没有选中的平台账号', 'error', batchTransferMessage);
            return;
        }

        try {
            showMessage('正在批量转移平台账号...', 'info', batchTransferMessage);

            // 准备批量转移数据
            const transfers = Array.from(selectedAccounts).map(phone => ({
                phone: phone,
                newHolderId: newHolderId
            }));

            // 使用优化的批量转移API，避免并发问题
            const response = await fetch(`${API_URL}/api/user/batch-transfer-platform-accounts`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_id: currentUser.id,
                    transfers: transfers,
                    sessionId: sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const { successCount, failCount } = data;

                if (failCount === 0) {
                    showMessage(`批量转移成功！共转移 ${successCount} 个平台账号`, 'success', batchTransferMessage);
                    setTimeout(() => {
                        hideBatchTransferModal();
                        clearAllSelection();
                        loadPlatformAccounts();
                        showMessage(`批量转移成功！共转移 ${successCount} 个平台账号`, 'success');
                    }, 1500);
                } else {
                    // 显示详细的失败信息
                    const failedAccounts = data.results.filter(r => !r.success).map(r => r.phone).join(', ');
                    showMessage(`部分转移成功：成功 ${successCount} 个，失败 ${failCount} 个。失败账号：${failedAccounts}`, 'warning', batchTransferMessage);
                    setTimeout(() => {
                        loadPlatformAccounts();
                    }, 3000);
                }
            } else {
                showMessage(data.message || '批量转移失败', 'error', batchTransferMessage);
            }
        } catch (error) {
            showMessage('批量转移失败，请稍后重试', 'error', batchTransferMessage);
            console.error('批量转移平台账号失败:', error);
        }
    }

    function showPlatformAccountsDisplay() {
      platformAccountsDisplay.classList.remove('hidden');
      loadPlatformAccounts();
    }

    function hidePlatformAccountsDisplay() {
      platformAccountsDisplay.classList.add('hidden');
    }

    // 搜索平台账号
    function searchPlatformAccounts() {
      const searchTerm = document.getElementById('platform-search-input').value.trim();
      if (searchTerm) {
        // 如果有搜索词，过滤显示结果
        const filteredAccounts = allPlatformAccounts.filter(account =>
          account.platform_phone.includes(searchTerm) ||
          (account.username && account.username.includes(searchTerm))
        );
        displayPlatformAccounts(filteredAccounts);
      } else {
        // 如果没有搜索词，显示所有账号
        displayPlatformAccounts(allPlatformAccounts);
      }
    }

    // 编辑平台账号
    window.editPlatformAccount = function(platformPhone) {

      // 检查用户是否登录
      if (!currentUser || !currentUser.id) {
        alert('请先登录');
        return;
      }

      // 从当前数据中找到要编辑的账号
      const accountToEdit = allPlatformAccounts.find(account => account.phone === platformPhone);
      if (!accountToEdit) {
        alert('未找到要编辑的平台账号');
        return;
      }

      // 检查权限：主账号可以编辑所有账号，子账号只能编辑归属于自己的账号
      const isMainAccount = currentUser.account_type === '主账号';
      if (!isMainAccount && accountToEdit.current_holder_id !== currentUser.id) {
        alert('您没有权限编辑此平台账号');
        return;
      }

      // 填充编辑表单
      platformPhoneInput.value = accountToEdit.phone || '';
      platformTypeSelect.value = accountToEdit.login_type || '';
      platformSessionidInput.value = accountToEdit.sessionid || '';

      // 在编辑模式下禁用手机号字段（手机号是唯一标识符，不应修改）
      platformPhoneInput.disabled = true;

      // 设置编辑模式标识
      window.currentEditingPhone = platformPhone;

      // 显示模态框
      addPlatformAccountModal.classList.remove('hidden');

      // 更改模态框标题
      const modalTitle = addPlatformAccountModal.querySelector('.modal-title');
      if (modalTitle) {
        modalTitle.textContent = '编辑平台账号';
      }
    }

    // 删除平台账号
    window.deletePlatformAccount = async function(platformPhone) {

      // 检查用户是否登录
      if (!currentUser || !currentUser.id) {
        alert('请先登录');
        return;
      }

      // 从当前数据中找到要删除的账号
      const accountToDelete = allPlatformAccounts.find(account => account.phone === platformPhone);
      if (!accountToDelete) {
        alert('未找到要删除的平台账号');
        return;
      }

      // 检查权限：主账号可以删除所有账号，子账号只能删除自己添加的账号
      const isMainAccount = currentUser.account_type === '主账号';
      if (!isMainAccount && accountToDelete.owner_id !== currentUser.id) {
        alert('您没有权限删除此平台账号');
        return;
      }

      if (!confirm(`确定要删除平台账号 ${platformPhone} 吗？此操作不可撤销。`)) {
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/user/platform-accounts`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            phone: platformPhone,
            userId: currentUser.id,
            sessionId: sessionId
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          alert('平台账号删除成功');
          loadPlatformAccounts(); // 刷新列表
        } else {
          alert(data.message || '删除平台账号失败');
        }
      } catch (error) {
        console.error('删除平台账号失败:', error);
        alert('网络错误，请稍后重试');
      }
    }

    // 将批量选择函数设为全局
    window.handleAccountSelection = handleAccountSelection;

    // 转移平台账号
    window.transferPlatformAccount = async function(platformPhone) {

      // 检查用户是否登录
      if (!currentUser || !currentUser.id) {
        alert('请先登录');
        return;
      }

      // 检查权限：只有主账号可以转移
      if (currentUser.account_type !== '主账号') {
        alert('只有主账号可以转移平台账号');
        return;
      }

      // 从当前数据中找到要转移的账号
      const accountToTransfer = allPlatformAccounts.find(account => account.phone === platformPhone);
      if (!accountToTransfer) {
        alert('未找到要转移的平台账号');
        return;
      }

      // 显示转移模态框
      await showTransferPlatformAccountModal(platformPhone);
    }

    // 显示添加平台账号模态框
    function showAddPlatformAccountModal() {
      // 清空表单
      platformPhoneInput.value = '';
      platformTypeSelect.value = '';
      contentTypeSelect.value = '';
      platformSessionidInput.value = '';
      platformAccountCreateMessage.classList.add('hidden');

      // 在添加模式下启用手机号字段
      platformPhoneInput.disabled = false;

      // 重置编辑模式标识
      window.currentEditingPhone = null;

      // 重置模态框标题
      const modalTitle = addPlatformAccountModal.querySelector('.modal-title');
      if (modalTitle) {
        modalTitle.textContent = '添加平台账号';
      }

      addPlatformAccountModal.classList.remove('hidden');
    }

    // 隐藏添加平台账号模态框
    function hideAddPlatformAccountModal() {
      addPlatformAccountModal.classList.add('hidden');

      // 清空表单
      platformPhoneInput.value = '';
      platformTypeSelect.value = '';
      contentTypeSelect.value = '';
      platformSessionidInput.value = '';
      platformAccountCreateMessage.classList.add('hidden');

      // 重置手机号字段状态
      platformPhoneInput.disabled = false;

      // 清理编辑模式状态
      window.currentEditingPhone = null;

      // 重置模态框标题
      const modalTitle = addPlatformAccountModal.querySelector('.modal-title');
      if (modalTitle) {
        modalTitle.textContent = '添加平台账号';
      }
    }

    // 显示转移平台账号模态框
    async function showTransferPlatformAccountModal(platformPhone) {
      currentTransferAccountPhone = platformPhone;
      transferNewHolderSelect.value = '';
      transferAccountMessage.classList.add('hidden');

      // 获取主账号和子账号列表
      await loadTransferUserOptions();

      transferPlatformAccountModal.classList.remove('hidden');
    }

    // 隐藏转移平台账号模态框
    function hideTransferPlatformAccountModal() {
      transferPlatformAccountModal.classList.add('hidden');
      currentTransferAccountPhone = null;
    }

    // 加载转移用户选项
    async function loadTransferUserOptions() {
      try {
        // 清空现有选项
        transferNewHolderSelect.innerHTML = '<option value="">请选择用户...</option>';

        // 添加主账号选项（当前用户）
        const mainOption = document.createElement('option');
        mainOption.value = currentUser.id;
        mainOption.textContent = `${currentUser.phone} (主账号)`;
        transferNewHolderSelect.appendChild(mainOption);

        // 获取子账号列表
        const response = await fetch(`${API_URL}/api/user/sub-accounts?ownerId=${currentUser.id}&sessionId=${sessionId}`);
        const data = await response.json();

        if (data.success && data.subAccounts) {
          data.subAccounts.forEach(subAccount => {
            const option = document.createElement('option');
            option.value = subAccount.id;
            option.textContent = `${subAccount.phone} (子账号)`;
            transferNewHolderSelect.appendChild(option);
          });
        }
      } catch (error) {
        console.error('加载用户选项失败:', error);
        showMessage('加载用户列表失败', 'error', transferAccountMessage);
      }
    }

    // 确认转移平台账号
    async function confirmTransferPlatformAccount() {
      const newHolderId = parseInt(transferNewHolderSelect.value);

      if (!newHolderId || !currentTransferAccountPhone) {
        showMessage('请选择有效的用户', 'error', transferAccountMessage);
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/user/transfer-platform-account`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            phone: currentTransferAccountPhone,
            new_holder_id: newHolderId,
            user_id: currentUser.id,
            sessionId: sessionId
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          showMessage('平台账号转移成功', 'success', transferAccountMessage);
          setTimeout(() => {
            hideTransferPlatformAccountModal();
            loadPlatformAccounts(); // 刷新列表
            alert('平台账号转移成功');
          }, 1500);
        } else {
          showMessage(data.message || '转移失败', 'error', transferAccountMessage);
        }
      } catch (error) {
        console.error('转移平台账号失败:', error);
        showMessage('网络错误，请稍后重试', 'error', transferAccountMessage);
      }
    }

    // 添加平台账号
    function addPlatformAccount() {

      // 检查用户是否登录
      if (!currentUser) {
        alert('请先登录');
        return;
      }

      // 检查用户ID
      if (!currentUser.id) {
        alert('用户信息不完整，请重新登录');
        return;
      }

      showAddPlatformAccountModal();
    }

    // 确认添加平台账号
    async function confirmAddPlatformAccount() {
      const phone = platformPhoneInput.value.trim();
      const platformType = platformTypeSelect.value.trim();
      const contentType = contentTypeSelect.value.trim();
      const sessionid = platformSessionidInput.value.trim();

      // 验证必要字段
      if (!phone) {
        showMessage('请输入平台手机号', 'error', platformAccountCreateMessage);
        return;
      }

      if (!platformType) {
        showMessage('请选择平台', 'error', platformAccountCreateMessage);
        return;
      }

      if (!contentType) {
        showMessage('请选择内容类型', 'error', platformAccountCreateMessage);
        return;
      }

      if (!sessionid) {
        showMessage('请输入SessionID', 'error', platformAccountCreateMessage);
        return;
      }

      // 检查用户登录状态
      if (!currentUser) {
        showMessage('用户未登录，请先登录', 'error', platformAccountCreateMessage);
        return;
      }

      if (!currentUser.id) {
        showMessage('用户信息不完整，请重新登录', 'error', platformAccountCreateMessage);
        return;
      }

      // 构建账号数据，其他字段使用"-"代替
      const isEditMode = window.currentEditingPhone;
      const accountData = {
        phone: isEditMode ? window.currentEditingPhone : phone, // 编辑模式使用原始手机号
        platform: platformType,
        login_type: contentType,
        team_tag: '-',
        data_update_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
        login_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
        username: '-',
        sessionid: sessionid,
        homepage_url: '-',
        is_verified: '否',
        drafts_count: '-',
        stats: {
          followers: '-',
          total_reads: '-',
          total_income: '-',
          yesterday_reads: '-',
          yesterday_income: '-',
          credit_score: '-',
          can_withdraw_amount: '-'
        },
        account_status: '正常'
      };

      try {
        let response;

        if (isEditMode) {
          // 编辑模式
          const isMainAccount = currentUser.account_type === '主账号';
          response = await fetch(`${API_URL}/api/user/platform-accounts`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              phone: window.currentEditingPhone,
              user_id: currentUser.id,
              is_main_account: isMainAccount,
              account_data: accountData,
              sessionId: sessionId
            })
          });
        } else {
          // 添加模式
          response = await fetch(`${API_URL}/api/user/platform-accounts`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              account_data: accountData,
              owner_id: currentUser.id,
              current_holder_id: currentUser.id,
              sessionId: sessionId
            })
          });
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          const successMessage = isEditMode ? '平台账号更新成功' : '平台账号添加成功，收益数据正在获取中...';
          showMessage(successMessage, 'success', platformAccountCreateMessage);
          setTimeout(() => {
            hideAddPlatformAccountModal();
            loadPlatformAccounts(); // 刷新平台账号列表
            showMessage(successMessage, 'success');
          }, 1500);
        } else {
          const errorMessage = isEditMode ? '更新平台账号失败' : '添加平台账号失败';
          showMessage(data.message || errorMessage, 'error', platformAccountCreateMessage);
        }
      } catch (error) {
        console.error('添加平台账号失败:', error);
        showMessage('网络错误，请稍后重试', 'error', platformAccountCreateMessage);
      }
    }



    // 删除子账号
    async function deleteSubAccount(subAccountId, phone) {
      if (!confirm(`确定要删除子账号 ${phone} 吗？此操作不可恢复。`)) {
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/user/delete-sub-account`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ownerId: currentUser.id,
            subAccountId: subAccountId,
            sessionId: sessionId
          })
        });

        const data = await response.json();

        if (data.success) {
          showMessage('子账号删除成功', 'success');
          loadSubAccounts(); // 刷新子账号列表
        } else {
          showMessage(data.message || '删除子账号失败', 'error');
        }
      } catch (error) {
        console.error('删除子账号失败:', error);
        showMessage('网络错误，请稍后重试', 'error');
      }
    }

    // 显示登录表单
    function showLoginForm() {
      authContainer.classList.remove('hidden');
      userContainer.classList.add('hidden');
      loginForm.reset();
      registerForm.reset();
    }

    // 连接SSE
    function connectSSE() {
      if (sseConnection && sseConnection.readyState === EventSource.OPEN) {
        return; // 已经连接
      }

      if (sessionId) {
        const clientId = localStorage.getItem('clientId') || generateClientId();
        localStorage.setItem('clientId', clientId);

        const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&clientId=${clientId}`;

        sseConnection = new EventSource(sseUrl);

        sseConnection.onopen = () => {
          // SSE连接已建立
        };

        sseConnection.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            if (data.type === 'force_logout') {
              showMessage(data.message, 'error');
              handleLogout(false);
            } else if (data.type === 'connected') {
              // SSE连接确认，无需处理
            } else if (data.type === 'sub_account_platform_added') {
              handleSubAccountPlatformAddedNotification(data);
            } else if (data.type === 'platform_account_received') {
              handlePlatformAccountReceivedNotification(data);
            } else if (data.type === 'platform_accounts_batch_received') {
              handlePlatformAccountsBatchReceivedNotification(data);
            } else if (data.type === 'platform_account_deleted_notification') {
              handlePlatformAccountDeletedNotification(data);
            }
          } catch (error) {
            console.error('处理SSE消息时出错:', error);
          }
        };

        sseConnection.onerror = (error) => {
          console.error('SSE 错误:', error);
          // EventSource会自动重连，无需手动处理
        };
      }
    }

    function generateClientId() {
      return 'client_' + Math.random().toString(36).substring(2, 15);
    }

    function showForceLoginModal() {
      forceLoginModal.classList.remove('hidden');
    }

    function hideForceLoginModal() {
      forceLoginModal.classList.add('hidden');
    }

    function checkModalState() {
      forceLoginModal.classList.add('hidden');
    }

    function showUpgradeModal(latestVersion) {
      document.getElementById('current-version').textContent = clientVersionInput.value;
      document.getElementById('latest-version').textContent = latestVersion;
      upgradeModal.classList.remove('hidden');
    }

    function hideUpgradeModal() {
      upgradeModal.classList.add('hidden');
    }

    // 处理子账号平台账号添加通知
    function handleSubAccountPlatformAddedNotification(data) {

      // 显示通知消息
      if (data.message) {
        showMessage(data.message, 'info');
      }

      // 如果当前在平台账号页面，刷新数据
      const platformAccountsSection = document.querySelector('.platform-accounts-section');
      if (platformAccountsSection && !platformAccountsSection.classList.contains('hidden')) {
        // 延迟刷新，确保数据库已更新
        setTimeout(() => {
          loadPlatformAccounts();
        }, 1000);
      }
    }

    // 处理平台账号转移/添加接收通知
    function handlePlatformAccountReceivedNotification(data) {
      // 显示通知消息
      if (data.message) {
        // 根据通知来源选择不同的样式
        const messageType = data.data && data.data.addedByUserType === '管理员' ? 'info' : 'success';
        showMessage(data.message, messageType);
      }

      // 直接刷新数据（用户界面只有一个页面）
      setTimeout(() => {
        if (typeof loadPlatformAccounts === 'function') {
          loadPlatformAccounts();
        }
      }, 1000);
    }

    // 处理平台账号删除通知
    function handlePlatformAccountDeletedNotification(data) {
      // 显示通知消息
      if (data.message) {
        // 根据通知来源和是否是当前用户操作选择不同的样式
        let messageType = 'warning';
        if (data.data && data.data.deletedByUserType === '管理员') {
          messageType = 'info';
        } else if (data.data && data.data.isCurrentUser) {
          messageType = 'success'; // 自己删除的显示为成功
        }
        showMessage(data.message, messageType);
      }

      // 直接刷新数据（用户界面只有一个页面）
      setTimeout(() => {
        if (typeof loadPlatformAccounts === 'function') {
          loadPlatformAccounts();
        }
      }, 1000);
    }

    // 处理批量平台账号转移接收通知
    function handlePlatformAccountsBatchReceivedNotification(data) {
      // 显示通知消息
      if (data.message) {
        // 根据通知来源选择不同的样式
        const messageType = data.data && data.data.operatorType === '管理员' ? 'info' : 'success';
        showMessage(data.message, messageType);
      }

      // 直接刷新数据（用户界面只有一个页面）
      setTimeout(() => {
        if (typeof loadPlatformAccounts === 'function') {
          loadPlatformAccounts();
        }
      }, 1000);
    }

    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const phone = document.getElementById('login-phone').value.trim();
      const password = document.getElementById('login-password').value;
      const clientVersion = clientVersionInput.value.trim();
      tempLoginData = { phone, password, clientVersion };

      try {
        const response = await fetch(`${API_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phone, password, clientVersion })
        });
        const data = await response.json();
        if (data.success) {
          showMessage('登录成功！', 'success');
          sessionId = data.sessionId;
          localStorage.setItem('sessionId', sessionId);
          // 直接使用登录接口返回的用户信息
          if (data.user) {
            showLoggedInUser(phone, data.user);
          } else {
            // 如果没有用户信息，则调用loadUserInfo获取
            await loadUserInfo(phone);
          }
          connectSSE();
          // checkActivationStatus(); // 已移除，激活状态在loadUserInfo中处理
        } else if (data.requireForceLogin) {
          showForceLoginModal();
        } else if (data.needUpgrade) {
          showUpgradeModal(data.latestVersion);
        } else {
          showMessage(data.message || '登录失败，请检查您的凭据。', 'error');
        }
      } catch (error) {
        showMessage('登录请求失败，请稍后重试。', 'error');
        console.error('登录失败:', error);
      }
    });

    confirmForceLoginBtn.addEventListener('click', async () => {
      if (!tempLoginData) {
        hideForceLoginModal();
        return;
      }
      try {
        const { phone, password, clientVersion } = tempLoginData;
        const response = await fetch(`${API_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            phone,
            password,
            forceLogin: true,
            clientVersion
          })
        });
        const data = await response.json();
        if (data.success) {
          hideForceLoginModal();
          showMessage('登录成功！', 'success');
          sessionId = data.sessionId;
          localStorage.setItem('sessionId', sessionId);
          // 直接使用登录接口返回的用户信息
          if (data.user) {
            showLoggedInUser(phone, data.user);
          } else {
            // 如果没有用户信息，则调用loadUserInfo获取
            await loadUserInfo(phone);
          }
          connectSSE();
          // checkActivationStatus(); // 已移除，激活状态在loadUserInfo中处理
        } else if (data.needUpgrade) {
          hideForceLoginModal();
          showUpgradeModal(data.latestVersion);
        } else {
          hideForceLoginModal();
          showMessage(data.message || '强制登录失败。', 'error');
        }
      } catch (error) {
        hideForceLoginModal();
        showMessage('强制登录请求失败。', 'error');
        console.error('强制登录失败:', error);
      }
      tempLoginData = null;
    });

    cancelForceLoginBtn.addEventListener('click', () => {
      hideForceLoginModal();
      tempLoginData = null;
    });

    registerForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const phone = document.getElementById('register-phone').value.trim();
      const password = document.getElementById('register-password').value;
      const confirmPassword = document.getElementById('register-confirm-password').value;

      if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致。', 'error');
        return;
      }

      try {
        const response = await fetch(`${API_URL}/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phone, password })
        });
        const data = await response.json();
        if (data.success) {
          showMessage('注册成功，请登录。', 'success');
          loginTab.click();
        } else {
          showMessage(data.message || '注册失败。', 'error');
        }
      } catch (error) {
        showMessage('注册请求失败。', 'error');
        console.error('注册失败:', error);
      }
    });

    logoutBtn.addEventListener('click', () => handleLogout(true));

    async function handleLogout(sendRequest = true) {
      // 先清除sessionId
      const currentSessionId = sessionId;
      sessionId = null;

      if (sseConnection) {
        sseConnection.close();
        sseConnection = null;
      }

      if (sendRequest && currentSessionId) {
        try {
          await fetch(`${API_URL}/auth/logout`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sessionId: currentSessionId })
          });
        } catch (error) {
          console.error('退出登录失败:', error);
        }
      }

      localStorage.removeItem('sessionId');
      showLoginForm();
      // Optionally show a logout message
      // showMessage('您已成功退出登录。', 'info');
    }

    forceLoginModal.addEventListener('click', (e) => {
      if (e.target === forceLoginModal) {
        hideForceLoginModal();
        tempLoginData = null;
      }
    });

    document.addEventListener('DOMContentLoaded', async () => {
      await checkSession();
      checkModalState();
    });

    // 修复：移除已废弃的activation-status接口调用
    // 激活状态现在通过登录接口直接返回，在loadUserInfo中处理
    function checkActivationStatus() {
      // 这个函数现在由loadUserInfo处理，保留空函数以避免调用错误
    }

    // 格式化日期为易读格式
    function formatDate(date) {
      return date.getFullYear() + '-' + 
             padZero(date.getMonth() + 1) + '-' + 
             padZero(date.getDate()) + ' ' + 
             padZero(date.getHours()) + ':' + 
             padZero(date.getMinutes());
    }

    // 补零函数
    function padZero(num) {
      return num < 10 ? '0' + num : num;
    }

    // 将deleteSubAccount函数设为全局函数，以便在HTML中调用
    window.deleteSubAccount = deleteSubAccount;

    // 子账号管理事件监听器
    addSubAccountBtn.addEventListener('click', showCreateSubAccountModal);
    refreshSubAccountsBtn.addEventListener('click', loadSubAccounts);
    cancelCreateSubAccountBtn.addEventListener('click', hideCreateSubAccountModal);
    confirmCreateSubAccountBtn.addEventListener('click', createSubAccount);

    // 平台账号管理事件监听器
    const searchPlatformBtn = document.getElementById('search-platform-btn');
    const addPlatformAccountBtn = document.getElementById('add-platform-account-btn');
    const platformSearchInput = document.getElementById('platform-search-input');

    searchPlatformBtn.addEventListener('click', searchPlatformAccounts);
    addPlatformAccountBtn.addEventListener('click', addPlatformAccount);

    // 添加平台账号模态框事件监听器
    cancelAddPlatformAccountBtn.addEventListener('click', hideAddPlatformAccountModal);
    confirmAddPlatformAccountBtn.addEventListener('click', confirmAddPlatformAccount);

    // 转移平台账号模态框事件监听器
    cancelTransferAccountBtn.addEventListener('click', hideTransferPlatformAccountModal);
    confirmTransferAccountBtn.addEventListener('click', confirmTransferPlatformAccount);

    // 批量转移相关事件监听器
    batchTransferBtn.addEventListener('click', showBatchTransferModal);
    selectAllBtn.addEventListener('click', selectAllAccounts);
    clearSelectionBtn.addEventListener('click', clearAllSelection);
    cancelBatchTransferBtn.addEventListener('click', hideBatchTransferModal);
    confirmBatchTransferBtn.addEventListener('click', confirmBatchTransfer);

    // 全选复选框事件
    selectAllCheckbox.addEventListener('change', function() {
        if (this.checked) {
            selectAllAccounts();
        } else {
            clearAllSelection();
        }
    });

    // 搜索输入框回车事件
    platformSearchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        searchPlatformAccounts();
      }
    });

    // 搜索输入框实时搜索
    platformSearchInput.addEventListener('input', () => {
      if (platformSearchInput.value.trim() === '') {
        displayPlatformAccounts(allPlatformAccounts);
      }
    });

    // 点击模态框外部关闭
    window.addEventListener('click', (event) => {
      if (event.target === createSubAccountModal) {
        hideCreateSubAccountModal();
      }
      if (event.target === addPlatformAccountModal) {
        hideAddPlatformAccountModal();
      }
      if (event.target === transferPlatformAccountModal) {
        hideTransferPlatformAccountModal();
      }
      if (event.target === batchTransferModal) {
        hideBatchTransferModal();
      }
    });

    // 子账号表单回车键提交
    subConfirmPasswordInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        createSubAccount();
      }
    });

    activateBtn.addEventListener('click', async () => {
      const activationCode = activationCodeInput.value.trim();
      if (!activationCode) {
        showMessage('请输入激活码。', 'error');
        return;
      }
      try {
        const response = await fetch(`${API_URL}/auth/activate-code`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId,
            code: activationCode
          })
        });
        const data = await response.json();
        if (data.success) {
          showMessage(`激活成功！已添加${data.days}天会员`, 'success');
          activationCodeInput.value = ''; // 清空输入框
          
          // 延迟一下再重新加载用户信息，确保后端数据已更新
          setTimeout(() => {
            loadUserInfo(currentUser.phone); // 重新加载用户信息以更新激活状态
          }, 500);
        } else {
          showMessage(data.message || '激活失败。', 'error');
        }
      } catch (error) {
        showMessage('激活请求失败。', 'error');
        console.error('激活失败:', error);
      }
    });
  </script>
</body>
</html>