import { DurableObject } from "cloudflare:workers";
import { initializeDB, resetYesterdayData } from "./database";
import { errorResponse } from "./utils";
import { handleUserRoutes } from "./userRoutes";
import { handleAdminRoutes } from "./adminRoutes";
import { smartBatchFetchAndUpdateData, scheduledBatchUpdateAllData } from "./dataFetcher-new";
import type { Env } from './types';

// 导出DurableObject类
export { UserAuthDO } from "./userAuthDO";
export { BatchProcessorDO } from "./batchProcessorDO";

/**
 * 推送平台账号数据给管理员和相关用户
 */
async function pushPlatformAccountData(env: Env, fetchResult: any): Promise<void> {
	try {
		console.log('开始推送平台账号数据...');

		// 获取UserAuthDO实例
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);

		const mainAccountGroups = fetchResult.mainAccountGroups;

		// 1. 推送给管理员 - 所有主账号的平台数据
		console.log('推送所有平台账号数据给管理员...');
		const adminNotifyRequest = new Request('https://dummy.com/push-platform-data', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				type: 'platform_data_update',
				target: 'admin',
				data: {
					mainAccountGroups,
					timestamp: new Date().toISOString()
				}
			})
		});

		const adminResponse = await userAuthDO.fetch(adminNotifyRequest as any);
		if (adminResponse.ok) {
			console.log('成功推送平台账号数据给管理员');
		} else {
			console.error('推送管理员平台数据失败:', await adminResponse.text());
		}

		// 2. 按主账号推送给对应的用户
		for (const [mainAccountId, groupData] of Object.entries(mainAccountGroups)) {
			try {
				console.log(`推送主账号 ${mainAccountId} 的平台数据给相关用户...`);

				const userNotifyRequest = new Request('https://dummy.com/push-platform-data', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						type: 'platform_data_update',
						target: 'user',
						mainAccountId: parseInt(mainAccountId),
						data: {
							mainAccountData: groupData,
							timestamp: new Date().toISOString()
						}
					})
				});

				const userResponse = await userAuthDO.fetch(userNotifyRequest as any);
				if (userResponse.ok) {
					console.log(`成功推送主账号 ${mainAccountId} 的平台数据给用户`);
				} else {
					console.error(`推送主账号 ${mainAccountId} 平台数据给用户失败:`, await userResponse.text());
				}
			} catch (error) {
				console.error(`推送主账号 ${mainAccountId} 数据异常:`, error);
			}
		}

		console.log('平台账号数据推送完成');
	} catch (error) {
		console.error('推送平台账号数据异常:', error);
	}
}

// 处理API请求 - 路由分发器
async function handleApiRequest(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const path = url.pathname;
	
	// 分发用户相关的API请求
	if (path.startsWith("/auth/") || path.startsWith("/api/user/")) {
		return handleUserRoutes(request, env);
	}
	
	// 分发管理员相关的API请求
	if (path.startsWith("/api/admin/") || path.startsWith("/api/auth/admin/")) {
		return handleAdminRoutes(request, env);
	}
	
	return errorResponse("未知的API路径", 404);
}

// 提供静态文件
async function serveStaticAsset(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
	const url = new URL(request.url);
	const path = url.pathname;

	// 尝试从静态资源中获取文件
	if (env.ASSETS) {
		// 如果路径是 /yanshiye，返回 yanshiye.html (演示页面)
		if (path === "/yanshiye") {
			return env.ASSETS.fetch(new Request(`${url.origin}/yanshiye.html`, request));
		}

		// 如果路径是 /admin，返回 admin.html (后台管理页面)
		if (path === "/admin") {
			return env.ASSETS.fetch(new Request(`${url.origin}/admin.html`, request));
		}

		// 如果路径是根路径 /，返回 404
		if (path === "/" || path === "") {
			return env.ASSETS.fetch(new Request(`${url.origin}/index.html`, request));
		}

		// 否则返回请求的资源
		let response = await env.ASSETS.fetch(request.clone() as Request<unknown, CfProperties<unknown>>);

		return response;
	}
	
	// 如果没有ASSETS或者没有找到对应资源，返回404
	return new Response("找不到请求的资源", { status: 404 });
}

export default {
	/**
	 * This is the standard fetch handler for a Cloudflare Worker
	 *
	 * @param request - The request submitted to the Worker from the client
	 * @param env - The interface to reference bindings declared in wrangler.jsonc
	 * @param ctx - The execution context of the Worker
	 * @returns The response to be sent back to the client
	 */
	async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
		// 初始化数据库（如果尚未初始化）
		try {
			// 尝试初始化数据库，最多重试3次
			let retries = 3;
			let success = false;

			while (retries > 0 && !success) {
				try {
					// 初始化数据库
					await initializeDB(env.DB);
					success = true;
				} catch (error) {
					retries--;
					if (retries > 0) {
						// 等待一段时间后重试
						await new Promise(resolve => setTimeout(resolve, 500));
					} else {
						// 只在最后一次失败时记录错误
						console.error("初始化数据库失败:", error);
					}
				}
			}
		} catch (error) {
			console.error("初始化数据库过程中发生错误:", error);
			// 继续执行，不要因为数据库初始化失败而阻止其他功能
		}

		const url = new URL(request.url);
		const path = url.pathname;

		try {
			// 处理API请求
			if (path.startsWith("/auth/") || path.startsWith("/api/admin/") || path.startsWith("/api/auth/admin/") || path.startsWith("/api/user/")) {
				return handleApiRequest(request, env);
			}

			// 提供静态页面
			return serveStaticAsset(request, env, ctx);
		} catch (error) {
			return new Response(`处理请求时出错: ${error}`, { status: 500 });
		}
	},

	/**
	 * 自动定时任务处理器
	 * - 每天北京时间24:00自动执行：清零所有账号的昨日阅读和昨日收益
	 * - 每天北京时间10:00自动执行：开始智能获取收益数据，根据需要动态调度后续任务
	 * - 完全自动化运行，无需管理员手动干预
	 */
	async scheduled(controller: ScheduledController, env: Env, ctx: ExecutionContext): Promise<void> {
		try {
			// 初始化数据库
			await initializeDB(env.DB);

			// 获取当前UTC时间
			const now = new Date();
			const hour = now.getUTCHours();
			const minute = now.getUTCMinutes();

			// 计算对应的北京时间用于日志显示
			const beijingHour = (hour + 8) % 24;

			console.log(`当前UTC时间: ${now.toISOString()}`);
			console.log(`UTC时间: ${hour}:${minute.toString().padStart(2, '0')}, 对应北京时间: ${beijingHour}:${minute.toString().padStart(2, '0')}`);



			// 每天北京时间10:00自动开始智能数据获取任务
			// UTC时间2:00对应北京时间10:00
			// 激活ScheduledTaskDO，先检查第一个账号的收益数据是否准备好
			if (hour === 2 && minute >= 0 && minute <= 5) {
				console.log(`=== 自动启动定时任务DO (UTC ${hour}:${minute.toString().padStart(2, '0')} / 北京时间 ${beijingHour}:${minute.toString().padStart(2, '0')}) ===`);

				try {
					// 获取BatchProcessorDO实例来处理定时任务
					const batchProcessorId = env.BATCH_PROCESSOR_DO.idFromName("batch_processor");
					const batchProcessorDO = env.BATCH_PROCESSOR_DO.get(batchProcessorId);

					// 启动定时任务
					const request = new Request('https://dummy.com/start-scheduled-task', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						}
					});

					const response = await batchProcessorDO.fetch(request as any);
					const result = await response.json() as any;

					if (result.success) {
						console.log(`✅ 定时任务启动成功: ${result.taskId}, 第一个账号: ${result.firstAccountPhone}`);
					} else {
						console.error(`❌ 定时任务启动失败: ${result.message}`);
					}
				} catch (error) {
					console.error(`定时任务启动异常: ${error}`);
				}
			}

			// 每天北京时间24:00自动执行数据清零任务
			// UTC时间16:00对应北京时间24:00
			// 自动清零所有账号的昨日数据，为新的一天做准备
			if (hour === 16 && minute >= 0 && minute <= 5) {
				const resetBeijingHour = (hour + 8) % 24;
				console.log(`=== 自动执行每日数据清零任务 (UTC ${hour}:${minute.toString().padStart(2, '0')} / 北京时间 ${resetBeijingHour}:${minute.toString().padStart(2, '0')}) ===`);
				const resetResult = await resetYesterdayData(env.DB);

				if (!resetResult.success) {
					console.error(`自动数据清零任务执行失败: ${resetResult.message}`);
				} else {
					console.log(`自动数据清零任务执行完成: ${resetResult.message}`);
				}
			}

		} catch (error) {
			console.error('定时任务执行过程中发生错误:', error);
		}
	},
} satisfies ExportedHandler<Env>;
