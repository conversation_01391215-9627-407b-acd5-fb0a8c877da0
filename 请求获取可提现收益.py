import requests
import json

# 1. Define the URL from the GET request line
# It's the host + the path/query parameters
url = "https://mp.toutiao.com/pgc/mp/income/income_statement_abstract?only_mid_income=false&days=30&app_id=1231"

# 2. Define the headers as a dictionary
# Copy all headers from your raw request, especially the 'Cookie' and 'User-Agent'
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "application/json, text/plain, */*",
    "Referer": "https://mp.toutiao.com/profile_v4/analysis/income-overview",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    # The cookie string is very long and is the most critical part for authentication
    "<PERSON>ie": "sessionid=f513080f2e55d54761d91ae15aa603e9",
}

# 3. Send the GET request
try:
    response = requests.get(url, headers=headers)

    # 4. Check the response and print the data
    # A status code of 200 means success!
    if response.status_code == 200:
        # Since the 'Accept' header asks for json, we can likely parse it as json
        data = response.json()
        print("✅ Request successful!")
        # Pretty-print the JSON data
        print(json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print(f"❌ Request failed with status code: {response.status_code}")
        print("Response Text:", response.text)

except requests.exceptions.RequestException as e:
    print(f"An error occurred: {e}")