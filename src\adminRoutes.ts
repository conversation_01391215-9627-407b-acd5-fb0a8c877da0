import {
	getAccounts,
	deleteAccount,
	generateActivationCodes,
	getActivationCodesList,
	getActivationRecordsList,
	adminLogin,
	changeAdminPassword,
	setClientVersion,
	getClientVersion,
	createSubAccount,
	getSubAccountsList,
	deleteSubAccountAPI,
	getUserInfo,
	getPlatformAccountsListAPI,
	batchUpdatePlatformAccountsAPI,
	addPlatformAccountAPI,
	updatePlatformAccountAPI,
	deletePlatformAccountAPI,
	getPlatformAccountByPhoneAPI,
	transferPlatformAccountAPI,
	batchTransferPlatformAccountsAPI,
	getAllMainAccountsPlatformAccountsAPI,
	manualFetchIncomeDataAPI,
	testResetYesterdayDataAPI,
	manualUpdateAllDataAPI,
	resetBatchStateAPI
} from "./adminApi";
import { getUserByPhone } from "./database";
import { errorResponse, handleCors } from "./utils";
import type { Env } from './types';
import type { Request as WorkerRequest } from '@cloudflare/workers-types';

// 验证管理员token的中间件
async function validateAdminAuth(request: Request, env: Env): Promise<{ valid: boolean; message?: string }> {
	try {
		// 从请求头或请求体中获取token
		let token: string | null = null;

		// 先尝试从Authorization头部获取
		const authHeader = request.headers.get('Authorization');

		if (authHeader && authHeader.startsWith('Bearer ')) {
			token = authHeader.substring(7);
		}

		// 如果没有Authorization头部，尝试从请求体获取
		if (!token) {
			try {
				const body = await request.clone().json<{ token?: string }>();
				token = body.token || null;
			} catch (error) {
				// 忽略解析错误
			}
		}

		// 最后尝试从查询参数获取
		if (!token) {
			const url = new URL(request.url);
			token = url.searchParams.get('token');
		}

		if (!token) {
			return { valid: false, message: "缺少管理员token" };
		}

		// 从KV中验证token
		const storedToken = await env.ADMIN_KV.get('admin_token');

		if (!storedToken || storedToken !== token) {
			return { valid: false, message: "无效的管理员token" };
		}

		return { valid: true };
	} catch (error) {
		console.error('管理员权限验证失败:', error);
		return { valid: false, message: `权限验证失败: ${error}` };
	}
}

// 处理管理员API路由
export async function handleAdminRoutes(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const path = url.pathname;
	
	// 处理预检请求
	if (request.method === "OPTIONS") {
		return handleCors();
	}

	// 处理管理后台API
	if (path.startsWith("/api/admin/")) {
		const adminPath = path.replace("/api/admin/", "");
		
		// 管理员登录不需要权限验证
		if (adminPath === "login") {
			if (request.method === "POST") {
				const { username, password } = await request.json<{ username: string; password: string }>();
				return adminLogin(env.ADMIN_KV, username, password);
			}
			return errorResponse("方法不允许", 405);
		}

		// 获取客户端版本不需要权限验证（公开API）
		if (adminPath === "get-client-version") {
			if (request.method === "GET") {
				return getClientVersion(env.ADMIN_KV);
			}
			return errorResponse("方法不允许", 405);
		}

		// 其他管理员API都需要权限验证
		const authValidation = await validateAdminAuth(request, env);
		if (!authValidation.valid) {
			return errorResponse(authValidation.message || "管理员权限验证失败", 403);
		}
		
		// 根据路径处理不同的管理员API
		switch (adminPath) {
			case "accounts":
				if (request.method === "GET") {
					const phone = url.searchParams.get("phone") || undefined;
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);
					return getAccounts(env.DB, userAuthDO, phone);
				}
				break;
				
			case "delete-account":
				if (request.method === "POST") {
					const { phone } = await request.json<{ phone: string }>();
					return deleteAccount(env.DB, phone);
				}
				break;
				
			case "generate-activation-codes":
				if (request.method === "POST") {
					const { type, count } = await request.json<{ type: string; count: number }>();
					return generateActivationCodes(env.DB, type as any, count);
				}
				break;
				
			case "activation-codes":
				if (request.method === "GET") {
					const type = url.searchParams.get("type") || undefined;
					return getActivationCodesList(env.DB, type as any);
				}
				break;
				
			case "activation-records":
				if (request.method === "GET") {
					return getActivationRecordsList(env.DB);
				}
				break;
				
			case "change-password":
				if (request.method === "POST") {
					const { oldPassword, newPassword } = await request.json<{ oldPassword: string; newPassword: string }>();
					return changeAdminPassword(env.ADMIN_KV, oldPassword, newPassword);
				}
				break;
				
			case "set-client-version":
				if (request.method === "POST") {
					const { version } = await request.json<{ version: string }>();
					return setClientVersion(env.ADMIN_KV, version);
				}
				break;
				
			case "create-sub-account":
				if (request.method === "POST") {
					const { ownerId, phone, password } = await request.json<{ ownerId: number; phone: string; password: string }>();
					return createSubAccount(env.DB, ownerId, phone, password);
				}
				break;
				
			case "sub-accounts":
			case "get-sub-accounts": // 兼容前端使用的路径
				if (request.method === "GET") {
					const ownerId = parseInt(url.searchParams.get("ownerId") || "0");
					if (!ownerId) {
						return errorResponse("缺少主账号ID", 400);
					}
					return getSubAccountsList(env.DB, ownerId);
				}
				break;
				
			case "delete-sub-account":
				if (request.method === "POST") {
					const { ownerId, subAccountId } = await request.json<{ ownerId: number; subAccountId: number }>();
					return deleteSubAccountAPI(env.DB, ownerId, subAccountId);
				}
				break;
				
			case "get-user-info":
				if (request.method === "GET") {
					const phone = url.searchParams.get("phone");
					if (!phone) {
						return errorResponse("缺少手机号参数", 400);
					}
					return getUserInfo(env.DB, phone);
				}
				break;
				
			case "force-logout":
				if (request.method === "POST") {
					const { phone } = await request.json<{ phone: string }>();
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);
					
					// 先查询用户ID
					const user = await getUserByPhone(env.DB, phone);
					if (!user) {
						return errorResponse("用户不存在", 404);
					}
					
					// 在Durable Object中处理强制下线
					const response = await userAuthDO.fetch(new Request('https://manbu.********.xyz/force-logout', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({ phone })
					}) as unknown as WorkerRequest);
					
					return response as unknown as Response;
				}
				break;
				
			// 平台账号管理相关API
			case "platform-accounts":
				if (request.method === "GET") {
					// 检查是否是获取所有主账号的平台账户信息
					const getAllMainAccounts = url.searchParams.get("get_all_main_accounts");
					if (getAllMainAccounts === "true") {
						const platformPhone = url.searchParams.get("platform_phone") || undefined;
						return getAllMainAccountsPlatformAccountsAPI(env.DB, platformPhone);
					}

					// 否则是获取特定用户的平台账户信息
					const user_id = parseInt(url.searchParams.get("user_id") || "0");
					const isMainAccount = url.searchParams.get("isMainAccount") === "true";
					const phone = url.searchParams.get("phone") || undefined;

					if (!user_id) {
						return errorResponse("缺少用户ID", 400);
					}

					return getPlatformAccountsListAPI(env.DB, user_id, isMainAccount, phone);
				} else if (request.method === "DELETE") {
					const { user_id, phone } = await request.json<{ user_id: number; phone: string }>();

					if (!user_id || !phone) {
						return errorResponse("缺少用户ID或手机号", 400);
					}

					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					return deletePlatformAccountAPI(env.DB, phone, user_id, userAuthDO);
				}
				break;
				
			case "batch-update-platform-accounts":
				if (request.method === "POST") {
					const { user_id, platformAccountsData } = await request.json<{ user_id: number; platformAccountsData: any }>();

					if (!user_id || !platformAccountsData) {
						return errorResponse("缺少用户ID或平台账号数据", 400);
					}

					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					return batchUpdatePlatformAccountsAPI(env.DB, user_id, platformAccountsData, userAuthDO);
				}
				break;
				
			case "add-platform-account":
				if (request.method === "POST") {
					const { accountData, ownerId, currentHolderId } = await request.json<{ accountData: any; ownerId: number; currentHolderId?: number }>();

					if (!accountData || !ownerId) {
						return errorResponse("缺少账号数据或所有者ID", 400);
					}

					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					return addPlatformAccountAPI(env.DB, accountData, ownerId, currentHolderId, userAuthDO);
				}
				break;
				
			case "update-platform-account":
				if (request.method === "POST") {
					const { user_id, phone, accountData, isMainAccount } = await request.json<{ user_id: number; phone: string; accountData: any; isMainAccount: boolean }>();

					if (!user_id || !phone || !accountData || typeof isMainAccount !== 'boolean') {
						return errorResponse("缺少必要参数", 400);
					}

					// 获取UserAuthDO实例用于推送通知
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					return updatePlatformAccountAPI(env.DB, phone, user_id, isMainAccount, accountData, undefined, userAuthDO);
				}
				break;
				
			case "delete-platform-account":
				if (request.method === "POST") {
					const { user_id, phone } = await request.json<{ user_id: number; phone: string }>();
					
					if (!user_id || !phone) {
						return errorResponse("缺少用户ID或手机号", 400);
					}
					
					return deletePlatformAccountAPI(env.DB, phone, user_id);
				}
				break;
				
			case "get-platform-account-by-phone":
				if (request.method === "GET") {
					const user_id = parseInt(url.searchParams.get("user_id") || "0");
					const phone = url.searchParams.get("phone");
					const isMainAccount = url.searchParams.get("isMainAccount") === "true";

					if (!user_id || !phone) {
						return errorResponse("缺少用户ID或手机号", 400);
					}

					return getPlatformAccountByPhoneAPI(env.DB, phone, user_id, isMainAccount);
				}
				break;
				
			case "transfer-platform-account":
				if (request.method === "POST") {
					const { user_id, phone, newHolderId } = await request.json<{ user_id: number; phone: string; newHolderId: number }>();

					if (!user_id || !phone || !newHolderId) {
						return errorResponse("缺少必要参数", 400);
					}

					// 获取UserAuthDO实例用于推送
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					// 执行转移操作
					const result = await transferPlatformAccountAPI(env.DB, phone, newHolderId, user_id, userAuthDO);



					return result;
				}
				break;
				
			case "batch-transfer-platform-accounts":
				if (request.method === "POST") {
					const { user_id, transfers } = await request.json<{ user_id: number; transfers: Array<{ phone: string; newHolderId: number }> }>();

					if (!user_id || !transfers || !Array.isArray(transfers) || transfers.length === 0) {
						return errorResponse("缺少用户ID或转移数据", 400);
					}

					// 获取UserAuthDO实例用于推送
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					// 执行批量转移操作
					const result = await batchTransferPlatformAccountsAPI(env.DB, user_id, transfers, userAuthDO);



					return result;
				}
				break;

			// 手动触发收益数据获取
			case "manual-fetch-income":
				if (request.method === "POST") {
					return manualFetchIncomeDataAPI(env.DB);
				}
				break;

			// 手动更新全部信息
			case "manual-update-all-data":
				if (request.method === "POST") {
					return manualUpdateAllDataAPI(env.DB, env);
				}
				break;



			case "test-reset-yesterday-data":
				if (request.method === "POST") {
					return testResetYesterdayDataAPI(env.DB);
				}
				break;

			// 重置批量处理状态
			case "reset-batch-state":
				if (request.method === "POST") {
					return resetBatchStateAPI(env);
				}
				break;

		}

		return errorResponse("未知的管理API路径或方法不允许", 404);
	}
	
	// 处理管理员的UserAuthDO相关API
	if (path.startsWith("/api/auth/admin/")) {
		const adminAuthPath = path.replace("/api/auth/admin/", "");
		
		// 验证管理员权限
		const authValidation = await validateAdminAuth(request, env);
		if (!authValidation.valid) {
			return errorResponse(authValidation.message || "管理员权限验证失败", 403);
		}
		
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);

		switch (adminAuthPath) {

				
			case "sse":
				// 管理员SSE连接
				// 获取token参数并传递给UserAuthDO
				const url = new URL(request.url);
				const token = url.searchParams.get('token');

				const targetUrl = new URL('https://manbu.********.xyz/admin/sse');
				if (token) {
					targetUrl.searchParams.set('token', token);
				}

				const response = await userAuthDO.fetch(new Request(targetUrl.toString(), {
					method: 'GET',
					headers: {
						'Accept': 'text/event-stream',
						'Cache-Control': 'no-cache'
					}
				}) as unknown as WorkerRequest);
				return response as unknown as Response;
		}

		return errorResponse("未知的管理员认证API路径", 404);
	}
	
	return errorResponse("未知的管理员路径", 404);
}
