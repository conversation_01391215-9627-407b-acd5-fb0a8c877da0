import { DurableObject } from 'cloudflare:workers';
import type { D1Database } from '@cloudflare/workers-types';
import { allDataFetcher, BatchProcessor, incomeDataFetcher } from './dataFetcher-new';
import { FetchResult } from './types';

// 批量处理进度状态
export interface BatchProgress {
  batchId: string;
  totalAccounts: number;
  processedAccounts: number;
  successCount: number;
  failureCount: number;
  currentBatch: number;
  totalBatches: number;
  status: 'waiting' | 'processing' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  lastUpdateTime: number;
  currentAccount?: string;
  results: FetchResult[];
  errorMessage?: string;
}

// 批量处理配置
export interface BatchConfig {
  batchSize: number; // 每批处理的账号数量
  delayBetweenBatches: number; // 批次间延迟（毫秒）
  delayBetweenAccounts: number; // 账号间延迟（毫秒）
}

// SSE 连接管理
interface SSEConnection {
  webSocket: WebSocket;
  batchId: string;
  connectedAt: number;
}

// 定时任务状态
interface ScheduledTaskState {
  taskId: string;
  status: 'waiting' | 'checking' | 'retrying' | 'processing' | 'batch_processing' | 'retry_processing' | 'completed' | 'failed';
  startTime: number;
  lastCheckTime: number;
  retryCount: number;
  maxRetries: number;
  retryInterval: number; // 30分钟 = 30 * 60 * 1000 毫秒
  firstAccountPhone?: string;
  firstAccountSessionId?: string;
  errorMessage?: string;
  // 智能重试相关
  currentRound: number; // 当前轮次
  maxRounds: number; // 最大轮次
  retryIntervalMinutes: number; // 重试间隔（分钟）
  notReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }>; // 未准备好的账号
}

/**
 * 批量处理器 Durable Object
 * 使用 Alarms 实现分批次顺序处理，通过 SSE 推送进度
 */
export class BatchProcessorDO extends DurableObject {
  private state: any;
  private db: D1Database;
  protected env: any;
  private currentProgress: BatchProgress | null = null;
  private accounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];
  private config: BatchConfig = {
    batchSize: 4,
    delayBetweenBatches: 500, // 0.5秒
    delayBetweenAccounts: 100  // 0.1秒
  };
  private scheduledTask: ScheduledTaskState | null = null;

  constructor(state: any, env: any) {
    super(state, env);
    this.state = state;
    this.db = env.DB;
    this.env = env;

    // 初始化时检查是否有未完成的批处理和定时任务
    this.state.blockConcurrencyWhile(async () => {
      const storedProgress = await this.state.storage.get('currentProgress') as BatchProgress;
      if (storedProgress && storedProgress.status === 'processing') {
        this.currentProgress = storedProgress;
        this.accounts = await this.state.storage.get('accounts') || [];
        console.log(`恢复未完成的批处理: ${storedProgress.batchId}`);
      }

      const storedTask = await this.state.storage.get('scheduledTask') as ScheduledTaskState;
      if (storedTask && ['checking', 'retrying'].includes(storedTask.status)) {
        this.scheduledTask = storedTask;
        console.log(`恢复未完成的定时任务: ${storedTask.taskId}`);
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();

    switch (action) {
      case 'start-batch':
        return this.handleStartBatch(request);

      case 'progress':
        return this.handleSSEConnection(request);

      case 'status':
        return this.handleGetStatus();

      case 'cancel':
        return this.handleCancelBatch();

      case 'reset':
        return this.handleResetBatch();

      case 'start-scheduled-task':
        return this.handleStartScheduledTask();

      case 'get-task-status':
        return this.handleGetTaskStatus();

      case 'cancel-task':
        return this.handleCancelTask();

      default:
        return new Response('Not Found', { status: 404 });
    }
  }

  /**
   * 启动批量处理
   */
  private async handleStartBatch(request: Request): Promise<Response> {
    try {
      const { config } = await request.json<{ config?: Partial<BatchConfig> }>();
      
      // 检查是否已有正在进行的批处理
      if (this.currentProgress && this.currentProgress.status === 'processing') {
        return new Response(JSON.stringify({
          success: false,
          message: '已有批处理正在进行中',
          batchId: this.currentProgress.batchId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 更新配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 获取所有需要处理的账号
      const processor = new BatchProcessor(this.db);
      this.accounts = await processor.getAllAccounts();

      if (this.accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到需要处理的账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 创建批处理进度
      const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const totalBatches = Math.ceil(this.accounts.length / this.config.batchSize);
      
      this.currentProgress = {
        batchId,
        totalAccounts: this.accounts.length,
        processedAccounts: 0,
        successCount: 0,
        failureCount: 0,
        currentBatch: 0,
        totalBatches,
        status: 'processing',
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        results: []
      };

      // 保存状态
      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.put('accounts', this.accounts);

      // 设置第一个 alarm 立即开始处理
      await this.state.storage.setAlarm(Date.now() + 100);

      console.log(`🚀 [DO+Alarms] 批量处理已启动: ${batchId}, 总账号数: ${this.accounts.length}, 分${totalBatches}批处理`);
      console.log(`⏰ [DO+Alarms] 第一个 Alarm 已设置，100ms后开始处理`);

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已启动',
        batchId,
        totalAccounts: this.accounts.length,
        totalBatches
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动批量处理失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 推送单个账号数据到前端
   */
  private async pushAccountDataToFrontend(
    account: { mainAccountId: number; phone: string; sessionid: string },
    accountData: any
  ): Promise<void> {
    try {
      // 获取 UserAuthDO 实例
      const id = this.env.USER_AUTH_DO.idFromName("auth");
      const userAuthDO = this.env.USER_AUTH_DO.get(id);

      console.log(`📤 [DO+Alarms] 开始推送账号 ${account.phone} 的数据到前端...`);

      // 获取完整的主账号平台数据
      const { getPlatformAccountsData } = await import('./database');
      const platformData = await getPlatformAccountsData(this.db, account.mainAccountId);

      if (!platformData.success || !platformData.accounts) {
        console.error(`获取主账号 ${account.mainAccountId} 平台数据失败，无法推送`);
        return;
      }

      const mainAccountData = platformData.accounts;
      const platformAccounts: any[] = [];

      // 构造平台账号数据
      for (const [phone, accountInfo] of Object.entries(mainAccountData)) {
        platformAccounts.push({
          ...accountInfo,
          phone  // 确保phone属性在最后，覆盖可能存在的重复属性
        });
      }

      const groupData = {
        mainAccountId: account.mainAccountId,
        platformAccounts: platformAccounts,
        totalAccounts: platformAccounts.length,
        lastUpdate: new Date().toISOString()
      };

      // 构造主账号组数据
      const mainAccountGroups = {
        [account.mainAccountId]: groupData
      };

      // 1. 推送给管理员
      console.log(`📤 [DO+Alarms] 推送账号 ${account.phone} 数据给管理员...`);
      const adminNotifyRequest = new Request('https://dummy.com/push-platform-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'platform_data_update',
          target: 'admin',
          data: {
            mainAccountGroups,
            timestamp: new Date().toISOString(),
            singleAccount: account.phone, // 标记这是单个账号的推送
            batchUpdate: true // 标记这是批量处理中的更新
          }
        })
      });

      const adminResponse = await userAuthDO.fetch(adminNotifyRequest);
      if (adminResponse.ok) {
        console.log(`✅ [DO+Alarms] 成功推送账号 ${account.phone} 数据给管理员`);
      } else {
        console.error(`❌ [DO+Alarms] 推送账号 ${account.phone} 给管理员失败:`, await adminResponse.text());
      }

      // 2. 推送给该主账号的用户
      console.log(`📤 [DO+Alarms] 推送账号 ${account.phone} 数据给主账号 ${account.mainAccountId} 的用户...`);
      const userNotifyRequest = new Request('https://dummy.com/push-platform-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'platform_data_update',
          target: 'user',
          mainAccountId: account.mainAccountId,
          data: {
            mainAccountData: groupData,
            timestamp: new Date().toISOString(),
            singleAccount: account.phone, // 标记这是单个账号的推送
            batchUpdate: true // 标记这是批量处理中的更新
          }
        })
      });

      const userResponse = await userAuthDO.fetch(userNotifyRequest);
      if (userResponse.ok) {
        console.log(`✅ [DO+Alarms] 成功推送账号 ${account.phone} 数据给用户`);
      } else {
        console.error(`❌ [DO+Alarms] 推送账号 ${account.phone} 给用户失败:`, await userResponse.text());
      }

      console.log(`🎉 [DO+Alarms] 账号 ${account.phone} 数据推送完成`);

    } catch (error) {
      console.error(`推送账号 ${account.phone} 数据到前端失败:`, error);
    }
  }

  /**
   * 通过管理员 SSE 推送批量处理进度
   */
  private async pushProgressToAdminSSE(progress: BatchProgress): Promise<void> {
    try {
      // 获取 UserAuthDO 实例
      const id = this.env.USER_AUTH_DO.idFromName("auth");
      const userAuthDO = this.env.USER_AUTH_DO.get(id);

      // 构造进度消息
      const progressMessage = {
        type: 'batch_progress',
        data: progress,
        timestamp: new Date().toISOString()
      };

      // 通过 UserAuthDO 推送到管理员 SSE 连接
      const request = new Request('https://dummy.com/admin/push-batch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(progressMessage)
      });

      await userAuthDO.fetch(request);
      console.log(`📡 [DO+Alarms] 进度已推送到管理员 SSE: ${progress.processedAccounts}/${progress.totalAccounts}`);
    } catch (error) {
      console.error('推送进度到管理员 SSE 失败:', error);
    }
  }

  /**
   * 处理 SSE 连接（已废弃，使用管理员 SSE 代替）
   */
  private async handleSSEConnection(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const batchId = url.searchParams.get('batchId');

    if (!batchId) {
      return new Response('Missing batchId parameter', { status: 400 });
    }

    // 创建一个简单的 SSE 流，返回当前状态
    let responseText = '';

    // 发送连接确认
    responseText += 'data: {"type":"connected","message":"SSE连接已建立"}\n\n';

    // 如果有当前进度，立即发送
    if (this.currentProgress && this.currentProgress.batchId === batchId) {
      const progressData = JSON.stringify({
        type: 'progress',
        data: this.currentProgress
      });
      responseText += `data: ${progressData}\n\n`;
    } else {
      // 发送空状态
      responseText += 'data: {"type":"progress","data":null}\n\n';
    }

    return new Response(responseText, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }

  /**
   * 获取当前状态
   */
  private async handleGetStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.currentProgress
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消批量处理
   */
  private async handleCancelBatch(): Promise<Response> {
    if (this.currentProgress && this.currentProgress.status === 'processing') {
      this.currentProgress.status = 'cancelled';
      this.currentProgress.lastUpdateTime = Date.now();

      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.deleteAlarm();

      console.log(`批量处理已取消: ${this.currentProgress.batchId}`);

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已取消'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      message: '没有正在进行的批量处理'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 强制重置批量处理状态
   */
  private async handleResetBatch(): Promise<Response> {
    try {
      console.log('🔄 强制重置批量处理状态...');

      // 清除所有状态
      this.currentProgress = null;
      this.accounts = [];

      // 删除存储的状态
      await this.state.storage.delete('currentProgress');
      await this.state.storage.delete('accounts');

      // 删除任何设置的 alarm
      await this.state.storage.deleteAlarm();

      console.log('✅ 批量处理状态已重置');

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理状态已重置'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('重置批量处理状态失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `重置失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Alarm 处理器 - 处理当前批次
   */
  async alarm(): Promise<void> {
    console.log('🔔 Durable Objects Alarm 被触发！');

    // 优先处理定时任务
    if (this.scheduledTask && ['checking', 'retrying'].includes(this.scheduledTask.status)) {
      await this.handleScheduledTaskAlarm();
      return;
    }

    // 处理批量任务
    if (!this.currentProgress || this.currentProgress.status !== 'processing') {
      console.log('❌ 没有需要处理的批次或状态不正确');
      return;
    }

    try {
      console.log(`🚀 [DO+Alarms] 开始处理第 ${this.currentProgress.currentBatch + 1}/${this.currentProgress.totalBatches} 批`);

      // 计算当前批次的账号范围
      const startIndex = this.currentProgress.currentBatch * this.config.batchSize;
      const endIndex = Math.min(startIndex + this.config.batchSize, this.accounts.length);
      const currentBatchAccounts = this.accounts.slice(startIndex, endIndex);

      console.log(`处理账号 ${startIndex + 1}-${endIndex}/${this.accounts.length}`);

      // 处理当前批次的账号
      for (let i = 0; i < currentBatchAccounts.length; i++) {
        const account = currentBatchAccounts[i];
        
        if (this.currentProgress.status !== 'processing') {
          console.log('批处理已被取消，停止处理');
          return;
        }

        this.currentProgress.currentAccount = account.phone;
        this.currentProgress.lastUpdateTime = Date.now();

        console.log(`📱 [DO+Alarms] 处理账号 ${account.phone} (${this.currentProgress.processedAccounts + 1}/${this.currentProgress.totalAccounts}) - 获取全部信息`);

        try {
          // 获取账号的全部数据（收益、信用分、粉丝数、账号信息、状态、阅读量、草稿箱）
          const fetchResult = await allDataFetcher(account.phone, account.sessionid);
          this.currentProgress.results.push(fetchResult);

          if (fetchResult.success && fetchResult.data) {
            // 更新数据库
            const processor = new BatchProcessor(this.db);
            const updateResult = await processor.updateDatabase(account, fetchResult.data);
            
            if (updateResult.success) {
              this.currentProgress.successCount++;
              console.log(`✅ [DO+Alarms] 账号 ${account.phone} 全部信息处理成功`);

              // 立即推送账号数据到前端
              await this.pushAccountDataToFrontend(account, fetchResult.data);
            } else {
              this.currentProgress.failureCount++;
              console.log(`❌ [DO+Alarms] 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);
            }
          } else {
            this.currentProgress.failureCount++;
            console.log(`❌ [DO+Alarms] 账号 ${account.phone} 数据获取失败: ${fetchResult.message}`);
          }

        } catch (error) {
          this.currentProgress.failureCount++;
          console.error(`处理账号 ${account.phone} 时发生异常:`, error);
          
          this.currentProgress.results.push({
            success: false,
            phone: account.phone,
            message: `处理异常: ${error}`
          });
        }

        this.currentProgress.processedAccounts++;
        this.currentProgress.lastUpdateTime = Date.now();

        // 保存进度
        await this.state.storage.put('currentProgress', this.currentProgress);

        // 推送进度到管理员 SSE
        await this.pushProgressToAdminSSE(this.currentProgress);

        // 账号间延迟
        if (i < currentBatchAccounts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenAccounts));
        }
      }

      // 更新批次计数
      this.currentProgress.currentBatch++;
      this.currentProgress.currentAccount = undefined;

      // 检查是否还有更多批次需要处理
      if (this.currentProgress.currentBatch < this.currentProgress.totalBatches) {
        // 设置下一个批次的 alarm
        await this.state.storage.setAlarm(Date.now() + this.config.delayBetweenBatches);
        console.log(`⏰ [DO+Alarms] 第 ${this.currentProgress.currentBatch}/${this.currentProgress.totalBatches} 批处理完成，${this.config.delayBetweenBatches}ms后处理下一批`);
      } else {
        // 所有批次处理完成
        this.currentProgress.status = 'completed';
        this.currentProgress.lastUpdateTime = Date.now();
        console.log(`🎉 [DO+Alarms] 所有批次处理完成！成功: ${this.currentProgress.successCount}, 失败: ${this.currentProgress.failureCount}`);
      }

      // 保存最终状态
      await this.state.storage.put('currentProgress', this.currentProgress);

      // 推送最终进度到管理员 SSE
      await this.pushProgressToAdminSSE(this.currentProgress);

    } catch (error) {
      console.error('批次处理失败:', error);

      if (this.currentProgress) {
        this.currentProgress.status = 'failed';
        this.currentProgress.errorMessage = String(error);
        this.currentProgress.lastUpdateTime = Date.now();
        await this.state.storage.put('currentProgress', this.currentProgress);
      }
    }
  }

  // ==================== 定时任务处理方法 ====================

  /**
   * 启动定时任务
   */
  private async handleStartScheduledTask(): Promise<Response> {
    try {
      // 检查是否已有正在进行的任务
      if (this.scheduledTask && ['checking', 'retrying', 'processing'].includes(this.scheduledTask.status)) {
        return new Response(JSON.stringify({
          success: false,
          message: `已有正在进行的任务: ${this.scheduledTask.taskId}`,
          taskId: this.scheduledTask.taskId,
          status: this.scheduledTask.status
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 获取第一个账号信息
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到任何平台账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const firstAccount = accounts[0];
      const taskId = `scheduled_task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 创建新任务状态
      this.scheduledTask = {
        taskId,
        status: 'checking',
        startTime: Date.now(),
        lastCheckTime: Date.now(),
        retryCount: 0,
        maxRetries: 10, // 最多重试10次（5小时）
        retryInterval: 30 * 60 * 1000, // 30分钟
        firstAccountPhone: firstAccount.phone,
        firstAccountSessionId: firstAccount.sessionid,
        // 智能重试相关
        currentRound: 0,
        maxRounds: 20, // 最多20轮重试
        retryIntervalMinutes: 3, // 3分钟重试间隔
        notReadyAccounts: []
      };

      // 保存状态
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 立即开始第一次检查
      await this.state.storage.setAlarm(Date.now() + 100);

      console.log(`🚀 [ScheduledTask] 定时任务已启动: ${taskId}, 第一个账号: ${firstAccount.phone}`);

      return new Response(JSON.stringify({
        success: true,
        message: '定时任务已启动',
        taskId,
        firstAccountPhone: firstAccount.phone
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动定时任务失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 获取定时任务状态
   */
  private async handleGetTaskStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      task: this.scheduledTask
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消定时任务
   */
  private async handleCancelTask(): Promise<Response> {
    if (this.scheduledTask) {
      this.scheduledTask.status = 'failed';
      this.scheduledTask.errorMessage = '任务被手动取消';
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 清除alarm（如果有的话）
      await this.state.storage.deleteAlarm();
    }

    return new Response(JSON.stringify({
      success: true,
      message: '任务已取消'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 处理定时任务的 Alarm
   */
  private async handleScheduledTaskAlarm(): Promise<void> {
    if (!this.scheduledTask) {
      console.log('❌ [ScheduledTask] 没有需要处理的定时任务');
      return;
    }

    try {
      switch (this.scheduledTask.status) {
        case 'checking':
        case 'retrying':
          await this.handleFirstAccountCheck();
          break;
        case 'batch_processing':
          await this.handleBatchProcessing();
          break;
        case 'retry_processing':
          await this.handleRetryProcessing();
          break;
        default:
          console.log(`❌ [ScheduledTask] 未知状态: ${this.scheduledTask.status}`);
      }
    } catch (error) {
      console.error('定时任务处理失败:', error);
      this.scheduledTask.status = 'failed';
      this.scheduledTask.errorMessage = `处理异常: ${error}`;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
    }
  }

  /**
   * 检查第一个账号的收益数据
   */
  private async handleFirstAccountCheck(): Promise<void> {
    console.log(`🔍 [ScheduledTask] 开始检查第一个账号收益数据: ${this.scheduledTask!.firstAccountPhone}`);

    // 获取第一个账号的收益数据
    const incomeResult = await incomeDataFetcher(
      this.scheduledTask!.firstAccountPhone!,
      this.scheduledTask!.firstAccountSessionId!
    );

    this.scheduledTask!.lastCheckTime = Date.now();

    if (incomeResult.success && incomeResult.isYesterdayIncomeReady) {
      // 收益数据已准备好，开始智能批量处理
      console.log(`✅ [ScheduledTask] 第一个账号收益数据已准备好，开始智能批量处理所有账号`);

      this.scheduledTask!.status = 'batch_processing';
      this.scheduledTask!.currentRound = 1;
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 立即开始第一轮批量处理
      await this.state.storage.setAlarm(Date.now() + 100);

    } else {
      // 收益数据还未准备好，需要重试
      this.scheduledTask!.retryCount++;

      if (this.scheduledTask!.retryCount >= this.scheduledTask!.maxRetries) {
        // 达到最大重试次数
        this.scheduledTask!.status = 'failed';
        this.scheduledTask!.errorMessage = `达到最大重试次数 (${this.scheduledTask!.maxRetries})，收益数据仍未准备好`;
        await this.state.storage.put('scheduledTask', this.scheduledTask);

        console.error(`❌ [ScheduledTask] 达到最大重试次数，任务失败`);
        return;
      }

      // 设置下次重试
      this.scheduledTask!.status = 'retrying';
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 设置30分钟后的alarm
      await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryInterval);

      console.log(`⏰ [ScheduledTask] 收益数据未准备好 (is_yesterday_income_ready: ${incomeResult.isYesterdayIncomeReady}), 30分钟后重试 (${this.scheduledTask!.retryCount}/${this.scheduledTask!.maxRetries})`);
    }
  }

  /**
   * 处理第一轮批量处理
   */
  private async handleBatchProcessing(): Promise<void> {
    console.log(`🚀 [ScheduledTask] 开始第${this.scheduledTask!.currentRound}轮智能批量处理`);

    // 启动智能批量处理
    const batchResult = await this.smartBatchUpdateAllData();

    if (batchResult.success) {
      // 检查是否还有未准备好的账号
      if (this.scheduledTask!.notReadyAccounts.length > 0) {
        console.log(`📊 [ScheduledTask] 第${this.scheduledTask!.currentRound}轮完成，还有 ${this.scheduledTask!.notReadyAccounts.length} 个账号未准备好，3分钟后重试`);

        this.scheduledTask!.status = 'retry_processing';
        this.scheduledTask!.currentRound++;
        await this.state.storage.put('scheduledTask', this.scheduledTask);

        // 设置3分钟后的alarm
        await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryIntervalMinutes * 60 * 1000);
      } else {
        // 所有账号都准备好了
        this.scheduledTask!.status = 'completed';
        await this.state.storage.put('scheduledTask', this.scheduledTask);
        console.log(`🎉 [ScheduledTask] 智能批量处理完成！所有账号收益数据都已准备好`);
      }
    } else {
      this.scheduledTask!.status = 'failed';
      this.scheduledTask!.errorMessage = `批量处理失败: ${batchResult.message}`;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      console.error(`❌ [ScheduledTask] 批量处理失败: ${batchResult.message}`);
    }
  }

  /**
   * 处理重试轮次
   */
  private async handleRetryProcessing(): Promise<void> {
    if (this.scheduledTask!.currentRound > this.scheduledTask!.maxRounds) {
      // 达到最大轮次
      this.scheduledTask!.status = 'completed';
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      console.log(`⏰ [ScheduledTask] 达到最大重试轮次 (${this.scheduledTask!.maxRounds})，任务完成。剩余 ${this.scheduledTask!.notReadyAccounts.length} 个账号未准备好`);
      return;
    }

    console.log(`🔄 [ScheduledTask] 开始第${this.scheduledTask!.currentRound}轮重试，处理 ${this.scheduledTask!.notReadyAccounts.length} 个未准备好的账号`);

    // 重试未准备好的账号
    const retryResult = await this.retryNotReadyAccounts();

    if (retryResult.success) {
      if (this.scheduledTask!.notReadyAccounts.length > 0) {
        console.log(`📊 [ScheduledTask] 第${this.scheduledTask!.currentRound}轮重试完成，还有 ${this.scheduledTask!.notReadyAccounts.length} 个账号未准备好，3分钟后继续重试`);

        this.scheduledTask!.currentRound++;
        await this.state.storage.put('scheduledTask', this.scheduledTask);

        // 设置3分钟后的alarm
        await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryIntervalMinutes * 60 * 1000);
      } else {
        // 所有账号都准备好了
        this.scheduledTask!.status = 'completed';
        await this.state.storage.put('scheduledTask', this.scheduledTask);
        console.log(`🎉 [ScheduledTask] 所有重试完成！所有账号收益数据都已准备好`);
      }
    } else {
      this.scheduledTask!.status = 'failed';
      this.scheduledTask!.errorMessage = `重试处理失败: ${retryResult.message}`;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      console.error(`❌ [ScheduledTask] 重试处理失败: ${retryResult.message}`);
    }
  }

  /**
   * 智能批量更新所有账号数据
   * 只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
   */
  private async smartBatchUpdateAllData(): Promise<{ success: boolean; message: string }> {
    try {
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        return { success: false, message: '没有找到任何平台账号' };
      }

      console.log(`📊 [SmartBatch] 开始智能批量处理 ${accounts.length} 个账号`);

      let successCount = 0;
      let failureCount = 0;
      const notReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];

      // 分批处理账号（4个一组）
      const batchSize = 4;
      for (let i = 0; i < accounts.length; i += batchSize) {
        const batch = accounts.slice(i, i + batchSize);
        console.log(`🔄 [SmartBatch] 处理第 ${Math.floor(i / batchSize) + 1} 批账号 (${batch.length} 个)`);

        // 并行处理当前批次的账号
        const batchPromises = batch.map(async (account) => {
          try {
            console.log(`  📱 处理账号: ${account.phone}`);

            // 获取账号数据
            const fetchResult = await allDataFetcher(account.phone, account.sessionid);

            if (fetchResult.success && fetchResult.data) {
              // 检查收益数据是否准备好
              const isIncomeReady = fetchResult.isYesterdayIncomeReady;
              console.log(`  📊 账号 ${account.phone} 收益数据状态: is_yesterday_income_ready = ${isIncomeReady}`);

              // 关键逻辑：只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
              if (!isIncomeReady) {
                // 收益数据未准备好，修改数据中的状态
                fetchResult.data.is_yesterday_income_ready = false;
                notReadyAccounts.push(account);
                console.log(`  ⚠️ 账号 ${account.phone} 收益数据未准备好，设置 is_yesterday_income_ready = false`);
              } else {
                // 收益数据已准备好
                fetchResult.data.is_yesterday_income_ready = true;
                console.log(`  ✅ 账号 ${account.phone} 收益数据已准备好，设置 is_yesterday_income_ready = true`);
              }

              // 更新数据库
              const updateResult = await processor.updateDatabase(account, fetchResult.data);
              if (updateResult.success) {
                successCount++;
                console.log(`  ✅ 账号 ${account.phone} 更新成功`);
              } else {
                failureCount++;
                console.log(`  ❌ 账号 ${account.phone} 更新失败: ${updateResult.message}`);
              }
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据获取失败: ${fetchResult.message}`);
            }
          } catch (error) {
            failureCount++;
            console.log(`  ❌ 账号 ${account.phone} 处理异常: ${error}`);
          }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);

        // 批次间延迟
        if (i + batchSize < accounts.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 更新未准备好的账号列表
      this.scheduledTask!.notReadyAccounts = notReadyAccounts;

      const message = `智能批量处理完成: 总计${accounts.length}个账号，成功${successCount}个，失败${failureCount}个，收益数据未准备好${notReadyAccounts.length}个`;
      console.log(`📊 [SmartBatch] ${message}`);

      return { success: true, message };

    } catch (error) {
      console.error('智能批量处理失败:', error);
      return { success: false, message: `智能批量处理失败: ${error}` };
    }
  }

  /**
   * 重试未准备好的账号
   */
  private async retryNotReadyAccounts(): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.scheduledTask!.notReadyAccounts || this.scheduledTask!.notReadyAccounts.length === 0) {
        return { success: true, message: '没有需要重试的账号' };
      }

      const processor = new BatchProcessor(this.db);
      const accountsToRetry = [...this.scheduledTask!.notReadyAccounts];

      console.log(`🔄 [Retry] 开始重试 ${accountsToRetry.length} 个未准备好的账号`);

      let successCount = 0;
      let failureCount = 0;
      const stillNotReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];

      // 分批处理账号（4个一组）
      const batchSize = 4;
      for (let i = 0; i < accountsToRetry.length; i += batchSize) {
        const batch = accountsToRetry.slice(i, i + batchSize);
        console.log(`🔄 [Retry] 处理第 ${Math.floor(i / batchSize) + 1} 批重试账号 (${batch.length} 个)`);

        // 并行处理当前批次的账号
        const batchPromises = batch.map(async (account) => {
          try {
            console.log(`  🔄 重试账号: ${account.phone}`);

            // 获取账号数据
            const fetchResult = await allDataFetcher(account.phone, account.sessionid);

            if (fetchResult.success && fetchResult.data) {
              // 检查收益数据是否准备好
              const isIncomeReady = fetchResult.isYesterdayIncomeReady;
              console.log(`  📊 账号 ${account.phone} 收益数据状态: is_yesterday_income_ready = ${isIncomeReady}`);

              // 关键逻辑：只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
              if (!isIncomeReady) {
                // 收益数据仍未准备好
                fetchResult.data.is_yesterday_income_ready = false;
                stillNotReadyAccounts.push(account);
                console.log(`  ⚠️ 账号 ${account.phone} 收益数据仍未准备好`);
              } else {
                // 收益数据现在准备好了
                fetchResult.data.is_yesterday_income_ready = true;
                console.log(`  ✅ 账号 ${account.phone} 收益数据现在准备好了！`);
              }

              // 更新数据库
              const updateResult = await processor.updateDatabase(account, fetchResult.data);
              if (updateResult.success) {
                successCount++;
                console.log(`  ✅ 账号 ${account.phone} 重试更新成功`);
              } else {
                failureCount++;
                console.log(`  ❌ 账号 ${account.phone} 重试更新失败: ${updateResult.message}`);
              }
            } else {
              failureCount++;
              stillNotReadyAccounts.push(account); // 获取失败的账号继续重试
              console.log(`  ❌ 账号 ${account.phone} 重试数据获取失败: ${fetchResult.message}`);
            }
          } catch (error) {
            failureCount++;
            stillNotReadyAccounts.push(account); // 异常的账号继续重试
            console.log(`  ❌ 账号 ${account.phone} 重试处理异常: ${error}`);
          }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);

        // 批次间延迟
        if (i + batchSize < accountsToRetry.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 更新仍未准备好的账号列表
      this.scheduledTask!.notReadyAccounts = stillNotReadyAccounts;

      const message = `重试处理完成: 重试${accountsToRetry.length}个账号，成功${successCount}个，失败${failureCount}个，仍未准备好${stillNotReadyAccounts.length}个`;
      console.log(`🔄 [Retry] ${message}`);

      return { success: true, message };

    } catch (error) {
      console.error('重试处理失败:', error);
      return { success: false, message: `重试处理失败: ${error}` };
    }
  }
}
