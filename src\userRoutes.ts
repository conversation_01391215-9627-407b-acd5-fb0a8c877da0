import { handleRegister, handleLogin, handleLogout, handleCheckSession, handleActivateCode, handleSSERequest } from "./userApi";
import {
	getUserInfo,
	getSubAccountsList,
	createSubAccount,
	deleteSubAccountAPI,
	getPlatformAccountsListAPI,
	addPlatformAccountAPI,
	updatePlatformAccountAPI,
	deletePlatformAccountAPI,
	transferPlatformAccountAPI,
	batchTransferPlatformAccountsAPI,
	batchUpdatePlatformAccountsAPI
} from "./adminApi";
import { errorResponse, handleCors } from "./utils";
import type { Env } from './types';
import type { Request as WorkerRequest } from '@cloudflare/workers-types';

// 验证用户会话的中间件
async function validateUserSession(sessionId: string, env: Env): Promise<{ valid: boolean; userId?: number; phone?: string; message?: string }> {
	if (!sessionId) {
		return { valid: false, message: "缺少会话ID" };
	}
	
	try {
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);
		
		const response = await userAuthDO.fetch(new Request('https://manbu.********.xyz/check-session', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ sessionId })
		}) as unknown as WorkerRequest);
		
		const result = await response.json<{ 
			valid: boolean; 
			message?: string; 
			userId?: number; 
			phone?: string;
		}>();
		
		return result;
	} catch (error) {
		return { valid: false, message: `会话验证失败: ${error}` };
	}
}

// 处理用户API路由
export async function handleUserRoutes(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const path = url.pathname;
	
	// 处理预检请求
	if (request.method === "OPTIONS") {
		return handleCors();
	}

	// 处理 /auth/ 路径的用户API
	if (path.startsWith("/auth/")) {
		const authPath = path.replace("/auth/", "");
		
		// 处理SSE连接请求
		if (authPath === "sse") {
			return handleSSERequest(request, env);
		}
		
		// 处理其他认证API
		switch (authPath) {
			case "register":
				// 注册是公开API，不需要权限验证
				return handleRegister(request, env);
				
			case "login":
				// 登录是公开API，不需要权限验证
				return handleLogin(request, env);
				
			case "logout":
				// 登出需要会话验证
				return handleLogout(request, env);
				
			case "check-session":
				// 会话检查本身就是验证功能
				return handleCheckSession(request, env);
				
			case "activate-code":
				// 激活码使用需要会话验证
				return handleActivateCode(request, env);
				
			default:
				return errorResponse("未知的认证路径", 404);
		}
	}

	// 处理 /api/user/ 路径的用户API（需要会话验证）
	if (path.startsWith("/api/user/")) {
		const userPath = path.replace("/api/user/", "");

		// 从请求中获取sessionId进行权限验证
		let sessionId: string | null = null;
		let requestBody: any = null;

		try {
			if (request.method === "GET") {
				// GET请求从查询参数获取sessionId
				sessionId = url.searchParams.get("sessionId");
			} else {
				// POST/PUT/DELETE请求从请求体获取sessionId，同时保存完整的body
				requestBody = await request.json<any>();
				sessionId = requestBody.sessionId || null;
			}
		} catch (error) {
			return errorResponse("请求格式错误", 400);
		}

		// 验证用户会话
		const sessionValidation = await validateUserSession(sessionId || "", env);
		if (!sessionValidation.valid) {
			return errorResponse(sessionValidation.message || "会话无效", 401);
		}

		// 从会话验证结果中获取用户ID
		const userId = sessionValidation.userId;
		if (!userId) {
			return errorResponse("无法获取用户ID", 401);
		}

		// 根据路径处理不同的用户API
		switch (userPath) {
			case "user-info":
				if (request.method === "GET") {
					const phone = url.searchParams.get("phone");
					if (!phone) {
						return errorResponse("缺少手机号参数", 400);
					}
					return getUserInfo(env.DB, phone);
				}
				break;

			case "sub-accounts":
				if (request.method === "GET") {
					const ownerId = parseInt(url.searchParams.get("ownerId") || "0");
					if (!ownerId) {
						return errorResponse("缺少主账号ID", 400);
					}
					return getSubAccountsList(env.DB, ownerId);
				}
				break;

			case "create-sub-account":
				if (request.method === "POST") {
					return createSubAccount(env.DB, requestBody.ownerId, requestBody.phone, requestBody.password);
				}
				break;

			case "delete-sub-account":
				if (request.method === "POST") {
					return deleteSubAccountAPI(env.DB, requestBody.ownerId, requestBody.subAccountId);
				}
				break;

			case "platform-accounts":
				if (request.method === "GET") {
					const userId = parseInt(url.searchParams.get("user_id") || "0");
					const isMainAccount = url.searchParams.get("is_main_account") === "true";
					if (!userId) {
						return errorResponse("缺少用户ID", 400);
					}
					const platformPhone = url.searchParams.get("phone") || undefined;
					return getPlatformAccountsListAPI(env.DB, userId, isMainAccount, platformPhone);
				} else if (request.method === "POST") {
					// 前端发送的是 account_data，使用会话中的 userId
					const accountData = requestBody.account_data;
					const currentHolderId = requestBody.current_holder_id || userId; // 如果没有指定持有者，默认为当前用户

					console.log('accountData:', accountData);
					console.log('userId:', userId);

					// 获取UserAuthDO实例用于推送通知
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					// 添加平台账号（传递userAuthDO以触发立即数据获取）
					const result = await addPlatformAccountAPI(env.DB, accountData, userId, currentHolderId, userAuthDO);

					// 如果添加成功，发送实时推送通知
					if (result.status === 200) {
						try {
							// 获取UserAuthDO实例用于推送
							const id = env.USER_AUTH_DO.idFromName("auth");
							const userAuthDO = env.USER_AUTH_DO.get(id);

							// 获取用户信息
							const userInfo = await env.DB.prepare(`
								SELECT phone, account_type, account_owner FROM users WHERE id = ?
							`).bind(userId).first<{ phone: string; account_type: string; account_owner: number | null }>();

							if (userInfo) {
								// 准备推送消息
								const notificationData = {
									type: 'platform_account_added',
									data: {
										userId: userId,
										userPhone: userInfo.phone,
										accountData: accountData,
										addedBy: userInfo.account_type,
										timestamp: new Date().toISOString()
									}
								};

								// 1. 推送给管理员（如果在线）
								const adminNotificationData = {
									type: 'platform_account_notification',
									message: `用户 ${userInfo.phone} 添加了新的平台账号: ${accountData.phone} (${accountData.username || '未知用户名'})`,
									data: {
										userId: userId,
										userPhone: userInfo.phone,
										platformPhone: accountData.phone,
										platformUsername: accountData.username || '未知用户名',
										timestamp: new Date().toISOString()
									}
								};



								await userAuthDO.fetch(new Request('https://internal/notify-admin', {
									method: 'POST',
									headers: { 'Content-Type': 'application/json' },
									body: JSON.stringify(adminNotificationData)
								}) as unknown as WorkerRequest);

								// 2. 推送给主账号体系内的所有用户（包括主账号和子账号）
								// 获取主账号ID
								const mainAccountId = userInfo.account_owner || userId;

								// 获取主账号体系内的所有用户
								const { getUserCompleteInfo } = await import('./database');
								const mainAccountInfo = await getUserCompleteInfo(env.DB, mainAccountId);

								if (mainAccountInfo.success && mainAccountInfo.userInfo) {
									const allUsers = [mainAccountInfo.userInfo];
									if (mainAccountInfo.userInfo.sub_accounts) {
										allUsers.push(...mainAccountInfo.userInfo.sub_accounts);
									}

									console.log('准备推送给主账号体系内的用户，总数:', allUsers.length);

									// 推送给所有用户（包括当前用户，用于多设备同步）
									for (const user of allUsers) {
										const userNotificationData = {
											type: 'platform_account_received',
											message: user.id === userId
												? `您添加了新的平台账号: ${accountData.phone} (${accountData.username || '未知用户名'})`
												: `${userInfo.account_type === '主账号' ? '主账号' : '子账号'} ${userInfo.phone} 添加了新的平台账号: ${accountData.phone} (${accountData.username || '未知用户名'})`,
											data: {
												addedByUserId: userId,
												addedByUserPhone: userInfo.phone,
												addedByUserType: userInfo.account_type,
												platformPhone: accountData.phone,
												platformUsername: accountData.username || '未知用户名',
												timestamp: new Date().toISOString(),
												isCurrentUser: user.id === userId
											}
										};

										console.log(`推送给用户 ${user.id} (${user.phone}):`, userNotificationData);

										await userAuthDO.fetch(new Request('https://internal/notify-user', {
											method: 'POST',
											headers: { 'Content-Type': 'application/json' },
											body: JSON.stringify({
												targetUserId: user.id,
												type: userNotificationData.type,
												message: userNotificationData.message,
												data: userNotificationData.data
											})
										}) as unknown as WorkerRequest);
									}
								}
							}
						} catch (pushError) {
							console.error('推送平台账号添加通知失败:', pushError);
							// 推送失败不影响主要功能，继续返回成功结果
						}
					}

					return result;
				} else if (request.method === "PUT") {
					return updatePlatformAccountAPI(env.DB, requestBody.phone, requestBody.userId, requestBody.isMainAccount, requestBody.accountData, requestBody.currentHolderId);
				} else if (request.method === "DELETE") {
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					return deletePlatformAccountAPI(env.DB, requestBody.phone, requestBody.userId, userAuthDO);
				}
				break;

			case "transfer-platform-account":
				if (request.method === "POST") {
					// 前端发送的是 new_holder_id 和 user_id，需要转换
					const phone = requestBody.phone;
					const newHolderId = requestBody.new_holder_id;
					const userId = requestBody.user_id;

					// 获取UserAuthDO实例用于推送
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					// 执行转移操作
					const result = await transferPlatformAccountAPI(env.DB, phone, newHolderId, userId, userAuthDO);



					return result;
				}
				break;

			case "batch-transfer-platform-accounts":
				if (request.method === "POST") {
					// 前端发送的是 transfers 数组和 user_id，需要转换
					const transfers = requestBody.transfers;
					const userId = requestBody.user_id;

					// 获取UserAuthDO实例用于推送
					const id = env.USER_AUTH_DO.idFromName("auth");
					const userAuthDO = env.USER_AUTH_DO.get(id);

					// 执行批量转移操作
					const result = await batchTransferPlatformAccountsAPI(env.DB, userId, transfers, userAuthDO);



					return result;
				}
				break;

			case "batch-update-platform-accounts":
				if (request.method === "POST") {
					// 批量更新平台账号信息
					const platformAccountsData = requestBody.platformAccountsData;
					const userId = requestBody.user_id;

					if (!platformAccountsData) {
						return errorResponse("缺少平台账号数据", 400);
					}

					if (!userId) {
						return errorResponse("缺少用户ID", 400);
					}

					return batchUpdatePlatformAccountsAPI(env.DB, userId, platformAccountsData);
				}
				break;

			default:
				return errorResponse("未知的用户API路径", 404);
		}
	}
	
	return errorResponse("未知的用户路径", 404);
}

// 用户权限验证辅助函数
export async function requireUserSession(request: Request, env: Env): Promise<{ success: boolean; sessionId?: string; userId?: number; phone?: string; response?: Response }> {
	try {
		let sessionId: string | null = null;
		
		// 尝试从不同位置获取sessionId
		const url = new URL(request.url);
		
		if (request.method === "GET") {
			sessionId = url.searchParams.get("sessionId");
		} else {
			const body = await request.json<{ sessionId?: string }>();
			sessionId = body.sessionId || null;
		}
		
		if (!sessionId) {
			return {
				success: false,
				response: errorResponse("缺少会话ID", 401)
			};
		}
		
		const validation = await validateUserSession(sessionId, env);
		if (!validation.valid) {
			return {
				success: false,
				response: errorResponse(validation.message || "会话无效", 401)
			};
		}
		
		return {
			success: true,
			sessionId,
			userId: validation.userId,
			phone: validation.phone
		};
	} catch (error) {
		return {
			success: false,
			response: errorResponse(`权限验证失败: ${error}`, 500)
		};
	}
}
